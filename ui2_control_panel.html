<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Human Interaction Recorder - Control Panel</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background-color: #f3f4f6;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .control-panel {
            width: 80%;
            max-width: 1200px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .header {
            background-color: #4a90e2;
            color: white;
            padding: 20px;
            border-radius: 10px 10px 0 0;
            text-align: center;
        }

        .header h1 {
            font-size: 24px;
        }

        .settings-container {
            display: flex;
            justify-content: space-around;
            padding: 20px;
        }

        .setting {
            flex: 1;
            padding: 10px;
        }

        .setting h2 {
            font-size: 18px;
            margin-bottom: 10px;
            color: #333;
        }

        .setting-control {
            padding: 5px 0;
        }

        .setting-control label {
            display: block;
            margin-bottom: 5px;
            color: #555;
        }

        .setting-control input {
            width: 100%;
            padding: 8px 10px;
            border-radius: 5px;
            border: 1px solid #ddd;
        }

        .setting-control button {
            margin-top: 10px;
            background-color: #4a90e2;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 8px 16px;
            cursor: pointer;
            transition: background-color 0.25s ease;
        }

        .setting-control button:hover {
            background-color: #357ab7;
        }

        .visualizer {
            background-color: #f7fafc;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.08);
        }

        .visualizer h2 {
            font-size: 18px;
            color: #333;
            margin-bottom: 15px;
        }

        .visualization-area {
            background-color: #e2e8f0;
            height: 300px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>
    <div class="control-panel">
        <div class="header">
            <h1>Human Interaction Control Panel</h1>
        </div>
        <div class="settings-container">
            <div class="setting">
                <h2>General Settings</h2>
                <div class="setting-control">
                    <label for="session-name">Session Name</label>
                    <input type="text" id="session-name" name="session-name" placeholder="Enter Session Name">
                </div>
                <div class="setting-control">
                    <label for="record-duration">Record Duration (min)</label>
                    <input type="number" id="record-duration" name="record-duration" min="1" max="120">
                </div>
                <div class="setting-control">
                    <button onclick="startRecording()">Start Recording</button>
                </div>
            </div>
            <div class="setting">
                <h2>Data Options</h2>
                <div class="setting-control">
                    <label>
                        <input type="checkbox" name="collect-mouse"> Mouse Movements
                    </label>
                </div>
                <div class="setting-control">
                    <label>
                        <input type="checkbox" name="collect-scroll"> Scrolling
                    </label>
                </div>
                <div class="setting-control">
                    <label>
                        <input type="checkbox" name="collect-typing"> Typing
                    </label>
                </div>
            </div>
        </div>
        <div class="visualizer">
            <h2>Data Visualization</h2>
            <div class="visualization-area">
                <p>Visualization comes here!</p>
            </div>
        </div>
    </div>

    <script>
        function startRecording() {
            alert('Recording started!');
        }
    </script>
</body>
</html>
