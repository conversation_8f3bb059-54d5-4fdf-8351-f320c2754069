<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Human Interaction Recorder - Minimalist Sidebar</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #0a0a0a;
            color: #ffffff;
            overflow: hidden;
        }

        .container {
            display: flex;
            height: 100vh;
        }

        .sidebar {
            width: 320px;
            background: linear-gradient(180deg, #1a1a1a 0%, #0d0d0d 100%);
            border-right: 1px solid #333;
            display: flex;
            flex-direction: column;
            position: relative;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.5);
        }

        .sidebar-header {
            padding: 30px 20px 20px;
            border-bottom: 1px solid #333;
        }

        .sidebar-header h1 {
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 5px;
        }

        .sidebar-header p {
            font-size: 12px;
            color: #888;
        }

        .recording-status {
            padding: 20px;
            border-bottom: 1px solid #333;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #333;
            transition: all 0.3s ease;
        }

        .status-dot.recording {
            background: #ff4444;
            box-shadow: 0 0 10px rgba(255, 68, 68, 0.5);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .status-text {
            font-size: 14px;
            color: #ccc;
        }

        .record-button {
            width: 100%;
            padding: 15px;
            background: #333;
            border: none;
            border-radius: 8px;
            color: #ffffff;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .record-button:hover {
            background: #444;
        }

        .record-button.recording {
            background: #ff4444;
        }

        .record-button.recording:hover {
            background: #ff3333;
        }

        .metrics-section {
            padding: 20px;
            border-bottom: 1px solid #333;
        }

        .metrics-title {
            font-size: 14px;
            color: #ccc;
            margin-bottom: 15px;
        }

        .metric-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .metric-label {
            font-size: 12px;
            color: #888;
        }

        .metric-value {
            font-size: 14px;
            font-weight: 600;
            color: #ffffff;
        }

        .settings-section {
            padding: 20px;
            flex: 1;
        }

        .settings-title {
            font-size: 14px;
            color: #ccc;
            margin-bottom: 15px;
        }

        .setting-item {
            margin-bottom: 15px;
        }

        .setting-label {
            display: block;
            font-size: 12px;
            color: #888;
            margin-bottom: 5px;
        }

        .setting-input {
            width: 100%;
            padding: 8px 12px;
            background: #222;
            border: 1px solid #333;
            border-radius: 6px;
            color: #ffffff;
            font-size: 12px;
        }

        .setting-input:focus {
            outline: none;
            border-color: #555;
        }

        .checkbox-container {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 10px;
        }

        .checkbox {
            width: 16px;
            height: 16px;
            appearance: none;
            background: #333;
            border-radius: 3px;
            position: relative;
            cursor: pointer;
        }

        .checkbox:checked {
            background: #4CAF50;
        }

        .checkbox:checked::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 10px;
        }

        .checkbox-label {
            font-size: 12px;
            color: #ccc;
            cursor: pointer;
        }

        .main-area {
            flex: 1;
            background: #000;
            position: relative;
            overflow: hidden;
        }

        .heatmap-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .heatmap-canvas {
            width: 100%;
            height: 100%;
        }

        .interaction-overlay {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 8px;
            min-width: 200px;
        }

        .overlay-title {
            font-size: 14px;
            color: #ccc;
            margin-bottom: 10px;
        }

        .interaction-log {
            max-height: 200px;
            overflow-y: auto;
            font-size: 11px;
            color: #888;
            line-height: 1.4;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 3px 0;
            border-bottom: 1px solid #222;
        }

        .log-entry:last-child {
            border-bottom: none;
        }

        .log-time {
            color: #555;
        }

        .log-type {
            color: #4CAF50;
            font-weight: 600;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .action-btn {
            flex: 1;
            padding: 10px;
            background: #333;
            border: none;
            border-radius: 6px;
            color: #ccc;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            background: #444;
            color: #ffffff;
        }

        .action-btn.primary {
            background: #4CAF50;
            color: #ffffff;
        }

        .action-btn.primary:hover {
            background: #45a049;
        }

        .mouse-trail {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #ff4444;
            border-radius: 50%;
            pointer-events: none;
            opacity: 0.7;
            animation: fadeOut 2s ease-out forwards;
        }

        @keyframes fadeOut {
            0% { opacity: 0.7; transform: scale(1); }
            100% { opacity: 0; transform: scale(0.5); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <div class="sidebar-header">
                <h1>Interaction Recorder</h1>
                <p>Real-time human behavior capture</p>
            </div>

            <div class="recording-status">
                <div class="status-indicator">
                    <div class="status-dot" id="statusDot"></div>
                    <span class="status-text" id="statusText">Ready to record</span>
                </div>
                <button class="record-button" id="recordButton" onclick="toggleRecording()">
                    Start Recording
                </button>
            </div>

            <div class="metrics-section">
                <div class="metrics-title">Live Metrics</div>
                <div class="metric-row">
                    <span class="metric-label">Mouse Events</span>
                    <span class="metric-value" id="mouseCount">0</span>
                </div>
                <div class="metric-row">
                    <span class="metric-label">Scroll Events</span>
                    <span class="metric-value" id="scrollCount">0</span>
                </div>
                <div class="metric-row">
                    <span class="metric-label">Key Events</span>
                    <span class="metric-value" id="keyCount">0</span>
                </div>
                <div class="metric-row">
                    <span class="metric-label">Duration</span>
                    <span class="metric-value" id="duration">00:00</span>
                </div>
            </div>

            <div class="settings-section">
                <div class="settings-title">Settings</div>
                
                <div class="setting-item">
                    <label class="setting-label">Session Name</label>
                    <input type="text" class="setting-input" value="Session_1" id="sessionName">
                </div>

                <div class="setting-item">
                    <label class="setting-label">Capture Rate (Hz)</label>
                    <input type="number" class="setting-input" value="60" min="10" max="120" id="captureRate">
                </div>

                <div class="setting-item">
                    <label class="setting-label">Data Types</label>
                    <div class="checkbox-container">
                        <input type="checkbox" class="checkbox" id="mouseMoves" checked>
                        <label class="checkbox-label" for="mouseMoves">Mouse Movement</label>
                    </div>
                    <div class="checkbox-container">
                        <input type="checkbox" class="checkbox" id="mouseClicks" checked>
                        <label class="checkbox-label" for="mouseClicks">Mouse Clicks</label>
                    </div>
                    <div class="checkbox-container">
                        <input type="checkbox" class="checkbox" id="scrollEvents" checked>
                        <label class="checkbox-label" for="scrollEvents">Scroll Events</label>
                    </div>
                    <div class="checkbox-container">
                        <input type="checkbox" class="checkbox" id="keyEvents" checked>
                        <label class="checkbox-label" for="keyEvents">Keyboard Input</label>
                    </div>
                    <div class="checkbox-container">
                        <input type="checkbox" class="checkbox" id="timingData" checked>
                        <label class="checkbox-label" for="timingData">Timing Data</label>
                    </div>
                </div>

                <div class="action-buttons">
                    <button class="action-btn primary" onclick="saveData()">Save</button>
                    <button class="action-btn" onclick="clearData()">Clear</button>
                </div>
            </div>
        </div>

        <div class="main-area">
            <div class="heatmap-container">
                <canvas class="heatmap-canvas" id="heatmapCanvas"></canvas>
            </div>
            
            <div class="interaction-overlay">
                <div class="overlay-title">Live Activity</div>
                <div class="interaction-log" id="activityLog">
                    <div class="log-entry">
                        <span class="log-time">00:00</span> - 
                        <span class="log-type">SYSTEM</span> - Ready to record
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let isRecording = false;
        let recordingData = [];
        let startTime = null;
        let counters = { mouse: 0, scroll: 0, key: 0 };
        let durationInterval = null;

        function toggleRecording() {
            const button = document.getElementById('recordButton');
            const statusDot = document.getElementById('statusDot');
            const statusText = document.getElementById('statusText');

            if (!isRecording) {
                startRecording();
                button.textContent = 'Stop Recording';
                button.classList.add('recording');
                statusDot.classList.add('recording');
                statusText.textContent = 'Recording...';
            } else {
                stopRecording();
                button.textContent = 'Start Recording';
                button.classList.remove('recording');
                statusDot.classList.remove('recording');
                statusText.textContent = 'Ready to record';
            }
        }

        function startRecording() {
            isRecording = true;
            startTime = Date.now();
            recordingData = [];
            counters = { mouse: 0, scroll: 0, key: 0 };
            
            durationInterval = setInterval(updateDuration, 1000);
            
            // Add event listeners
            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('click', handleClick);
            document.addEventListener('scroll', handleScroll);
            document.addEventListener('keydown', handleKeyDown);
            
            addLogEntry('Recording started', 'SYSTEM');
        }

        function stopRecording() {
            isRecording = false;
            clearInterval(durationInterval);
            
            // Remove event listeners
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('click', handleClick);
            document.removeEventListener('scroll', handleScroll);
            document.removeEventListener('keydown', handleKeyDown);
            
            addLogEntry('Recording stopped', 'SYSTEM');
        }

        function handleMouseMove(e) {
            if (!isRecording) return;
            
            const data = {
                type: 'mousemove',
                timestamp: Date.now() - startTime,
                x: e.clientX,
                y: e.clientY,
                pageX: e.pageX,
                pageY: e.pageY,
                movementX: e.movementX,
                movementY: e.movementY,
                velocity: Math.sqrt(e.movementX * e.movementX + e.movementY * e.movementY),
                target: e.target.tagName
            };
            
            recordingData.push(data);
            counters.mouse++;
            updateCounter('mouseCount', counters.mouse);
            
            // Create mouse trail
            createMouseTrail(e.clientX, e.clientY);
            
            // Update heatmap
            updateHeatmap(e.clientX, e.clientY);
        }

        function handleClick(e) {
            if (!isRecording) return;
            
            const data = {
                type: 'click',
                timestamp: Date.now() - startTime,
                x: e.clientX,
                y: e.clientY,
                button: e.button,
                ctrlKey: e.ctrlKey,
                altKey: e.altKey,
                shiftKey: e.shiftKey,
                target: e.target.tagName
            };
            
            recordingData.push(data);
            counters.mouse++;
            updateCounter('mouseCount', counters.mouse);
            addLogEntry(`Click at (${e.clientX}, ${e.clientY})`, 'CLICK');
        }

        function handleScroll(e) {
            if (!isRecording) return;
            
            const data = {
                type: 'scroll',
                timestamp: Date.now() - startTime,
                scrollX: window.scrollX,
                scrollY: window.scrollY,
                deltaX: e.deltaX,
                deltaY: e.deltaY,
                deltaMode: e.deltaMode
            };
            
            recordingData.push(data);
            counters.scroll++;
            updateCounter('scrollCount', counters.scroll);
            addLogEntry(`Scroll (${e.deltaX}, ${e.deltaY})`, 'SCROLL');
        }

        function handleKeyDown(e) {
            if (!isRecording) return;
            
            const data = {
                type: 'keydown',
                timestamp: Date.now() - startTime,
                key: e.key,
                code: e.code,
                ctrlKey: e.ctrlKey,
                altKey: e.altKey,
                shiftKey: e.shiftKey,
                target: e.target.tagName
            };
            
            recordingData.push(data);
            counters.key++;
            updateCounter('keyCount', counters.key);
            addLogEntry(`Key: ${e.key}`, 'KEY');
        }

        function updateCounter(id, value) {
            document.getElementById(id).textContent = value;
        }

        function updateDuration() {
            if (!startTime) return;
            const elapsed = Math.floor((Date.now() - startTime) / 1000);
            const minutes = Math.floor(elapsed / 60);
            const seconds = elapsed % 60;
            document.getElementById('duration').textContent = 
                `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }

        function addLogEntry(message, type) {
            const log = document.getElementById('activityLog');
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            
            const time = new Date().toLocaleTimeString().split(':').slice(0, 2).join(':');
            entry.innerHTML = `
                <span class="log-time">${time}</span> - 
                <span class="log-type">${type}</span> - ${message}
            `;
            
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
            
            // Keep only last 20 entries
            if (log.children.length > 20) {
                log.removeChild(log.firstChild);
            }
        }

        function createMouseTrail(x, y) {
            const trail = document.createElement('div');
            trail.className = 'mouse-trail';
            trail.style.left = x + 'px';
            trail.style.top = y + 'px';
            document.body.appendChild(trail);
            
            setTimeout(() => {
                if (trail.parentNode) {
                    trail.parentNode.removeChild(trail);
                }
            }, 2000);
        }

        function updateHeatmap(x, y) {
            const canvas = document.getElementById('heatmapCanvas');
            const ctx = canvas.getContext('2d');
            
            // Resize canvas if needed
            if (canvas.width !== window.innerWidth || canvas.height !== window.innerHeight) {
                canvas.width = window.innerWidth;
                canvas.height = window.innerHeight;
            }
            
            // Draw heatmap point
            const gradient = ctx.createRadialGradient(x, y, 0, x, y, 20);
            gradient.addColorStop(0, 'rgba(255, 68, 68, 0.3)');
            gradient.addColorStop(1, 'rgba(255, 68, 68, 0)');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(x - 20, y - 20, 40, 40);
        }

        function saveData() {
            if (recordingData.length === 0) {
                alert('No data to save. Please record some interactions first.');
                return;
            }
            
            const sessionName = document.getElementById('sessionName').value;
            const captureRate = document.getElementById('captureRate').value;
            
            const sessionData = {
                sessionName: sessionName,
                timestamp: new Date().toISOString(),
                captureRate: captureRate,
                totalEvents: recordingData.length,
                counters: counters,
                data: recordingData
            };
            
            const blob = new Blob([JSON.stringify(sessionData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${sessionName}_${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
            
            addLogEntry('Session saved', 'SYSTEM');
        }

        function clearData() {
            if (confirm('Clear all recorded data?')) {
                recordingData = [];
                counters = { mouse: 0, scroll: 0, key: 0 };
                updateCounter('mouseCount', 0);
                updateCounter('scrollCount', 0);
                updateCounter('keyCount', 0);
                document.getElementById('duration').textContent = '00:00';
                
                // Clear heatmap
                const canvas = document.getElementById('heatmapCanvas');
                const ctx = canvas.getContext('2d');
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // Clear log
                document.getElementById('activityLog').innerHTML = `
                    <div class="log-entry">
                        <span class="log-time">00:00</span> - 
                        <span class="log-type">SYSTEM</span> - Data cleared
                    </div>
                `;
            }
        }

        // Initialize canvas
        window.addEventListener('resize', () => {
            const canvas = document.getElementById('heatmapCanvas');
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        });
    </script>
</body>
</html>
