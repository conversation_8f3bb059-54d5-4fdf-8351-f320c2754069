<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Human Interaction Recorder - Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            font-size: 2.5em;
            color: #4a5568;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            color: #718096;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .recording-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .recording-panel h2 {
            color: #4a5568;
            margin-bottom: 20px;
            font-size: 1.8em;
        }

        .record-button {
            width: 100%;
            padding: 20px;
            font-size: 1.3em;
            border: none;
            border-radius: 10px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
            cursor: pointer;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .record-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
        }

        .record-button.recording {
            background: linear-gradient(135deg, #51cf66, #40c057);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }

        .metric-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }

        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #495057;
        }

        .metric-label {
            color: #6c757d;
            font-size: 0.9em;
            margin-top: 5px;
        }

        .live-feed {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .live-feed h2 {
            color: #4a5568;
            margin-bottom: 20px;
            font-size: 1.8em;
        }

        .interaction-log {
            background: #1a202c;
            border-radius: 10px;
            padding: 20px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
        }

        .log-entry {
            color: #e2e8f0;
            padding: 5px 0;
            border-bottom: 1px solid #2d3748;
        }

        .log-entry:last-child {
            border-bottom: none;
        }

        .log-timestamp {
            color: #4fd1c7;
            font-size: 0.8em;
        }

        .log-type {
            color: #fbb6ce;
            font-weight: bold;
        }

        .settings-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .settings-panel h2 {
            color: #4a5568;
            margin-bottom: 20px;
            font-size: 1.8em;
        }

        .setting-group {
            margin-bottom: 20px;
        }

        .setting-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #4a5568;
        }

        .setting-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 1em;
        }

        .setting-input:focus {
            outline: none;
            border-color: #667eea;
        }

        .checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 10px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .checkbox-item input[type="checkbox"] {
            width: 18px;
            height: 18px;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .recording-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #ff6b6b;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
            display: none;
        }

        .recording-indicator.active {
            display: block;
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="recording-indicator" id="recordingIndicator">
        🔴 RECORDING IN PROGRESS
    </div>

    <div class="container">
        <div class="header">
            <h1>Human Interaction Recorder</h1>
            <p>Capture detailed mouse movements, scrolling patterns, and typing behavior for AI training</p>
        </div>

        <div class="main-grid">
            <div class="recording-panel">
                <h2>Recording Control</h2>
                <button class="record-button" id="recordButton" onclick="toggleRecording()">
                    Start Recording
                </button>
                
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value" id="mouseEvents">0</div>
                        <div class="metric-label">Mouse Events</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="scrollEvents">0</div>
                        <div class="metric-label">Scroll Events</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="keyEvents">0</div>
                        <div class="metric-label">Key Events</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="duration">00:00</div>
                        <div class="metric-label">Duration</div>
                    </div>
                </div>

                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="saveSession()">Save Session</button>
                    <button class="btn btn-secondary" onclick="clearSession()">Clear Data</button>
                </div>
            </div>

            <div class="live-feed">
                <h2>Live Interaction Feed</h2>
                <div class="interaction-log" id="interactionLog">
                    <div class="log-entry">
                        <span class="log-timestamp">Ready to record...</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="settings-panel">
            <h2>Recording Settings</h2>
            <div class="setting-group">
                <label class="setting-label">Session Name</label>
                <input type="text" class="setting-input" placeholder="Enter session name..." value="Session_1">
            </div>
            
            <div class="setting-group">
                <label class="setting-label">Sampling Rate (ms)</label>
                <input type="number" class="setting-input" value="10" min="1" max="1000">
            </div>

            <div class="setting-group">
                <label class="setting-label">Data Collection Options</label>
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" id="mouseMovement" checked>
                        <label for="mouseMovement">Mouse Movement</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="mouseClicks" checked>
                        <label for="mouseClicks">Mouse Clicks</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="scrolling" checked>
                        <label for="scrolling">Scrolling</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="keystrokes" checked>
                        <label for="keystrokes">Keystrokes</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="timing" checked>
                        <label for="timing">Timing Data</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="viewport" checked>
                        <label for="viewport">Viewport Info</label>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let isRecording = false;
        let recordingData = [];
        let startTime = null;
        let mouseEventCount = 0;
        let scrollEventCount = 0;
        let keyEventCount = 0;
        let durationInterval = null;

        function toggleRecording() {
            const button = document.getElementById('recordButton');
            const indicator = document.getElementById('recordingIndicator');
            
            if (!isRecording) {
                startRecording();
                button.textContent = 'Stop Recording';
                button.classList.add('recording');
                indicator.classList.add('active');
            } else {
                stopRecording();
                button.textContent = 'Start Recording';
                button.classList.remove('recording');
                indicator.classList.remove('active');
            }
        }

        function startRecording() {
            isRecording = true;
            startTime = Date.now();
            recordingData = [];
            mouseEventCount = 0;
            scrollEventCount = 0;
            keyEventCount = 0;
            
            // Start duration counter
            durationInterval = setInterval(updateDuration, 1000);
            
            // Add event listeners
            document.addEventListener('mousemove', recordMouseMove);
            document.addEventListener('click', recordClick);
            document.addEventListener('scroll', recordScroll);
            document.addEventListener('keydown', recordKeydown);
            
            addLogEntry('Recording started', 'SYSTEM');
        }

        function stopRecording() {
            isRecording = false;
            clearInterval(durationInterval);
            
            // Remove event listeners
            document.removeEventListener('mousemove', recordMouseMove);
            document.removeEventListener('click', recordClick);
            document.removeEventListener('scroll', recordScroll);
            document.removeEventListener('keydown', recordKeydown);
            
            addLogEntry('Recording stopped', 'SYSTEM');
        }

        function recordMouseMove(e) {
            if (!isRecording) return;
            
            const data = {
                type: 'mousemove',
                timestamp: Date.now() - startTime,
                x: e.clientX,
                y: e.clientY,
                pageX: e.pageX,
                pageY: e.pageY,
                screenX: e.screenX,
                screenY: e.screenY,
                movementX: e.movementX,
                movementY: e.movementY,
                target: e.target.tagName
            };
            
            recordingData.push(data);
            mouseEventCount++;
            updateCounter('mouseEvents', mouseEventCount);
            
            if (mouseEventCount % 100 === 0) {
                addLogEntry(`Mouse: (${e.clientX}, ${e.clientY})`, 'MOUSE');
            }
        }

        function recordClick(e) {
            if (!isRecording) return;
            
            const data = {
                type: 'click',
                timestamp: Date.now() - startTime,
                x: e.clientX,
                y: e.clientY,
                button: e.button,
                target: e.target.tagName,
                ctrlKey: e.ctrlKey,
                altKey: e.altKey,
                shiftKey: e.shiftKey
            };
            
            recordingData.push(data);
            mouseEventCount++;
            updateCounter('mouseEvents', mouseEventCount);
            addLogEntry(`Click: (${e.clientX}, ${e.clientY}) Button: ${e.button}`, 'CLICK');
        }

        function recordScroll(e) {
            if (!isRecording) return;
            
            const data = {
                type: 'scroll',
                timestamp: Date.now() - startTime,
                scrollX: window.scrollX,
                scrollY: window.scrollY,
                deltaX: e.deltaX || 0,
                deltaY: e.deltaY || 0,
                deltaMode: e.deltaMode || 0
            };
            
            recordingData.push(data);
            scrollEventCount++;
            updateCounter('scrollEvents', scrollEventCount);
            addLogEntry(`Scroll: (${window.scrollX}, ${window.scrollY})`, 'SCROLL');
        }

        function recordKeydown(e) {
            if (!isRecording) return;
            
            const data = {
                type: 'keydown',
                timestamp: Date.now() - startTime,
                key: e.key,
                code: e.code,
                keyCode: e.keyCode,
                ctrlKey: e.ctrlKey,
                altKey: e.altKey,
                shiftKey: e.shiftKey,
                metaKey: e.metaKey,
                target: e.target.tagName
            };
            
            recordingData.push(data);
            keyEventCount++;
            updateCounter('keyEvents', keyEventCount);
            addLogEntry(`Key: ${e.key} (${e.code})`, 'KEY');
        }

        function updateCounter(id, value) {
            document.getElementById(id).textContent = value;
        }

        function updateDuration() {
            if (!startTime) return;
            const elapsed = Math.floor((Date.now() - startTime) / 1000);
            const minutes = Math.floor(elapsed / 60);
            const seconds = elapsed % 60;
            document.getElementById('duration').textContent = 
                `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }

        function addLogEntry(message, type) {
            const log = document.getElementById('interactionLog');
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.innerHTML = `
                <span class="log-timestamp">[${new Date().toLocaleTimeString()}]</span>
                <span class="log-type">[${type}]</span>
                ${message}
            `;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
        }

        function saveSession() {
            if (recordingData.length === 0) {
                alert('No data to save. Please record some interactions first.');
                return;
            }
            
            const sessionData = {
                sessionName: document.querySelector('input[placeholder="Enter session name..."]').value,
                timestamp: new Date().toISOString(),
                duration: Date.now() - startTime,
                totalEvents: recordingData.length,
                mouseEvents: mouseEventCount,
                scrollEvents: scrollEventCount,
                keyEvents: keyEventCount,
                data: recordingData
            };
            
            const blob = new Blob([JSON.stringify(sessionData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${sessionData.sessionName}_${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
            
            addLogEntry('Session saved successfully', 'SYSTEM');
        }

        function clearSession() {
            if (confirm('Are you sure you want to clear all recorded data?')) {
                recordingData = [];
                mouseEventCount = 0;
                scrollEventCount = 0;
                keyEventCount = 0;
                updateCounter('mouseEvents', 0);
                updateCounter('scrollEvents', 0);
                updateCounter('keyEvents', 0);
                document.getElementById('duration').textContent = '00:00';
                document.getElementById('interactionLog').innerHTML = 
                    '<div class="log-entry"><span class="log-timestamp">Data cleared - Ready to record...</span></div>';
            }
        }
    </script>
</body>
</html>
