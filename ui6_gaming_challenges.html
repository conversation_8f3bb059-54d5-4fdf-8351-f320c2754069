<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gaming-Style Human Interaction Challenges</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: radial-gradient(circle, #0f0f23 0%, #000000 100%);
            color: #00ff00;
            overflow: hidden;
            cursor: crosshair;
        }

        .hud {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: linear-gradient(180deg, rgba(0, 255, 0, 0.1) 0%, rgba(0, 0, 0, 0.8) 100%);
            border-bottom: 2px solid #00ff00;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            z-index: 1000;
        }

        .hud-left, .hud-right {
            display: flex;
            align-items: center;
            gap: 30px;
        }

        .hud-item {
            text-align: center;
            border: 1px solid #00ff00;
            padding: 10px 15px;
            background: rgba(0, 255, 0, 0.1);
            border-radius: 5px;
            min-width: 80px;
        }

        .hud-label {
            font-size: 10px;
            color: #00aa00;
            margin-bottom: 3px;
        }

        .hud-value {
            font-size: 16px;
            font-weight: bold;
            color: #00ff00;
        }

        .game-area {
            position: fixed;
            top: 80px;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 20%, rgba(0, 255, 0, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 60%, rgba(0, 255, 0, 0.1) 0%, transparent 50%),
                linear-gradient(0deg, rgba(0, 255, 0, 0.05) 0%, transparent 100%);
            position: relative;
            overflow: hidden;
        }

        .challenge-selector {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            flex-direction: column;
            gap: 15px;
            z-index: 100;
        }

        .challenge-btn {
            padding: 15px 30px;
            background: linear-gradient(45deg, #001100, #003300);
            border: 2px solid #00ff00;
            color: #00ff00;
            cursor: pointer;
            font-family: 'Courier New', monospace;
            font-size: 16px;
            text-transform: uppercase;
            letter-spacing: 2px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .challenge-btn:hover {
            background: linear-gradient(45deg, #003300, #005500);
            box-shadow: 0 0 20px rgba(0, 255, 0, 0.5);
            transform: scale(1.05);
        }

        .challenge-btn:before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 255, 0, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .challenge-btn:hover:before {
            left: 100%;
        }

        .target {
            position: absolute;
            border: 2px solid #00ff00;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.2s ease;
            background: radial-gradient(circle, rgba(0, 255, 0, 0.3) 0%, rgba(0, 255, 0, 0.1) 70%, transparent 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #00ff00;
        }

        .target:hover {
            transform: scale(1.1);
            box-shadow: 0 0 30px rgba(0, 255, 0, 0.8);
        }

        .target.hit {
            animation: targetHit 0.5s ease;
        }

        @keyframes targetHit {
            0% { transform: scale(1); }
            50% { transform: scale(1.5); background: #00ff00; }
            100% { transform: scale(0); opacity: 0; }
        }

        .obstacle {
            position: absolute;
            background: linear-gradient(45deg, #ff0000, #aa0000);
            border: 2px solid #ff0000;
            border-radius: 5px;
            cursor: not-allowed;
        }

        .power-up {
            position: absolute;
            width: 30px;
            height: 30px;
            background: radial-gradient(circle, #ffff00, #aaaa00);
            border: 2px solid #ffff00;
            border-radius: 50%;
            cursor: pointer;
            animation: powerUpPulse 2s infinite;
        }

        @keyframes powerUpPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.2); }
        }

        .moving-target {
            position: absolute;
            animation: moveTarget 3s linear infinite;
        }

        @keyframes moveTarget {
            0% { transform: translateX(0); }
            50% { transform: translateX(200px); }
            100% { transform: translateX(0); }
        }

        .combo-meter {
            position: fixed;
            top: 100px;
            right: 20px;
            width: 200px;
            height: 20px;
            background: rgba(0, 0, 0, 0.8);
            border: 2px solid #00ff00;
            border-radius: 10px;
            overflow: hidden;
        }

        .combo-fill {
            height: 100%;
            background: linear-gradient(90deg, #00ff00, #ffff00, #ff0000);
            transition: width 0.3s ease;
        }

        .typing-challenge {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80%;
            max-width: 800px;
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid #00ff00;
            border-radius: 10px;
            padding: 30px;
        }

        .typing-text {
            font-size: 18px;
            line-height: 1.8;
            margin-bottom: 20px;
            color: #00aa00;
        }

        .typing-input {
            width: 100%;
            height: 100px;
            background: rgba(0, 0, 0, 0.8);
            border: 2px solid #00ff00;
            border-radius: 5px;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            font-size: 16px;
            padding: 15px;
            resize: none;
        }

        .typing-input:focus {
            outline: none;
            box-shadow: 0 0 20px rgba(0, 255, 0, 0.5);
        }

        .scroll-track {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            overflow-y: auto;
            padding: 20px;
        }

        .scroll-content {
            height: 300vh;
            background: linear-gradient(
                180deg,
                rgba(0, 255, 0, 0.1) 0%,
                rgba(0, 0, 0, 0.8) 25%,
                rgba(0, 0, 255, 0.1) 50%,
                rgba(0, 0, 0, 0.8) 75%,
                rgba(255, 0, 0, 0.1) 100%
            );
            position: relative;
        }

        .scroll-checkpoint {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 50px;
            background: rgba(0, 255, 0, 0.3);
            border: 2px solid #00ff00;
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #00ff00;
        }

        .drag-item {
            position: absolute;
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, #00ff00, #00aa00);
            border: 2px solid #00ff00;
            border-radius: 10px;
            cursor: grab;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #000000;
            user-select: none;
        }

        .drag-item:active {
            cursor: grabbing;
        }

        .drop-zone {
            position: absolute;
            width: 100px;
            height: 100px;
            border: 3px dashed #00ff00;
            border-radius: 15px;
            background: rgba(0, 255, 0, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #00ff00;
        }

        .drop-zone.active {
            background: rgba(0, 255, 0, 0.3);
            border-color: #ffff00;
        }

        .mini-map {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 200px;
            height: 150px;
            background: rgba(0, 0, 0, 0.8);
            border: 2px solid #00ff00;
            border-radius: 10px;
            overflow: hidden;
        }

        .mini-map-content {
            width: 100%;
            height: 100%;
            position: relative;
            background: radial-gradient(circle, rgba(0, 255, 0, 0.2) 0%, transparent 50%);
        }

        .mini-map-dot {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #00ff00;
            border-radius: 50%;
        }

        .stats-panel {
            position: fixed;
            top: 100px;
            left: 20px;
            width: 250px;
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid #00ff00;
            border-radius: 10px;
            padding: 20px;
            display: none;
        }

        .stats-panel.active {
            display: block;
        }

        .stat-row {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 5px 0;
            border-bottom: 1px solid rgba(0, 255, 0, 0.3);
        }

        .achievement {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: linear-gradient(45deg, #ffff00, #ff8800);
            color: #000000;
            padding: 20px 40px;
            border-radius: 10px;
            font-size: 18px;
            font-weight: bold;
            text-align: center;
            box-shadow: 0 0 30px rgba(255, 255, 0, 0.8);
            animation: achievementPop 3s ease;
            z-index: 2000;
        }

        @keyframes achievementPop {
            0% { transform: translate(-50%, -50%) scale(0); }
            10% { transform: translate(-50%, -50%) scale(1.2); }
            20% { transform: translate(-50%, -50%) scale(1); }
            80% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
            100% { transform: translate(-50%, -50%) scale(0); opacity: 0; }
        }

        .particle {
            position: absolute;
            width: 3px;
            height: 3px;
            background: #00ff00;
            border-radius: 50%;
            pointer-events: none;
        }

        .path-tracer {
            position: absolute;
            width: 2px;
            height: 2px;
            background: rgba(0, 255, 0, 0.5);
            border-radius: 50%;
            pointer-events: none;
        }

        .hidden {
            display: none;
        }

        .challenge-complete {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.9);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 3000;
        }

        .challenge-complete h2 {
            color: #00ff00;
            font-size: 48px;
            margin-bottom: 20px;
            text-transform: uppercase;
        }

        .challenge-complete .stats {
            color: #00aa00;
            font-size: 18px;
            margin-bottom: 30px;
        }

        .restart-btn {
            padding: 15px 30px;
            background: linear-gradient(45deg, #001100, #003300);
            border: 2px solid #00ff00;
            color: #00ff00;
            cursor: pointer;
            font-family: 'Courier New', monospace;
            font-size: 16px;
            text-transform: uppercase;
            letter-spacing: 2px;
            transition: all 0.3s ease;
        }

        .restart-btn:hover {
            background: linear-gradient(45deg, #003300, #005500);
            box-shadow: 0 0 20px rgba(0, 255, 0, 0.5);
        }
    </style>
</head>
<body>
    <div class="hud">
        <div class="hud-left">
            <div class="hud-item">
                <div class="hud-label">SCORE</div>
                <div class="hud-value" id="score">0</div>
            </div>
            <div class="hud-item">
                <div class="hud-label">COMBO</div>
                <div class="hud-value" id="combo">0</div>
            </div>
            <div class="hud-item">
                <div class="hud-label">ACCURACY</div>
                <div class="hud-value" id="accuracy">100%</div>
            </div>
        </div>
        <div class="hud-center">
            <div class="hud-item">
                <div class="hud-label">CHALLENGE</div>
                <div class="hud-value" id="currentChallenge">SELECT</div>
            </div>
        </div>
        <div class="hud-right">
            <div class="hud-item">
                <div class="hud-label">TIME</div>
                <div class="hud-value" id="timer">00:00</div>
            </div>
            <div class="hud-item">
                <div class="hud-label">LEVEL</div>
                <div class="hud-value" id="level">1</div>
            </div>
            <div class="hud-item">
                <div class="hud-label">RECORDING</div>
                <div class="hud-value" id="recording">OFF</div>
            </div>
        </div>
    </div>

    <div class="combo-meter">
        <div class="combo-fill" id="comboFill" style="width: 0%"></div>
    </div>

    <div class="mini-map">
        <div class="mini-map-content" id="miniMap"></div>
    </div>

    <div class="stats-panel" id="statsPanel">
        <h3>LIVE STATS</h3>
        <div class="stat-row">
            <span>Mouse Speed:</span>
            <span id="mouseSpeed">0 px/s</span>
        </div>
        <div class="stat-row">
            <span>Precision:</span>
            <span id="precision">0%</span>
        </div>
        <div class="stat-row">
            <span>Reaction Time:</span>
            <span id="reactionTime">0ms</span>
        </div>
        <div class="stat-row">
            <span>Path Efficiency:</span>
            <span id="pathEfficiency">0%</span>
        </div>
        <div class="stat-row">
            <span>Scroll Smoothness:</span>
            <span id="scrollSmoothness">0%</span>
        </div>
        <div class="stat-row">
            <span>Typing Rhythm:</span>
            <span id="typingRhythm">0 WPM</span>
        </div>
    </div>

    <div class="game-area" id="gameArea">
        <div class="challenge-selector" id="challengeSelector">
            <button class="challenge-btn" onclick="startChallenge('aimTrainer')">AIM TRAINER</button>
            <button class="challenge-btn" onclick="startChallenge('speedClicker')">SPEED CLICKER</button>
            <button class="challenge-btn" onclick="startChallenge('typingRacer')">TYPING RACER</button>
            <button class="challenge-btn" onclick="startChallenge('scrollMaster')">SCROLL MASTER</button>
            <button class="challenge-btn" onclick="startChallenge('dragRacer')">DRAG RACER</button>
            <button class="challenge-btn" onclick="startChallenge('reactionTest')">REACTION TEST</button>
            <button class="challenge-btn" onclick="startChallenge('comboChallenge')">COMBO CHALLENGE</button>
            <button class="challenge-btn" onclick="startChallenge('endurance')">ENDURANCE MODE</button>
        </div>
    </div>

    <script>
        let gameState = {
            recording: false,
            currentChallenge: null,
            startTime: null,
            score: 0,
            combo: 0,
            accuracy: 100,
            level: 1,
            sessionData: {
                mouseEvents: [],
                keyEvents: [],
                scrollEvents: [],
                challenges: []
            }
        };

        let mouseTrail = [];
        let lastMousePos = { x: 0, y: 0 };
        let lastMouseTime = 0;

        function startChallenge(challengeType) {
            gameState.currentChallenge = challengeType;
            gameState.recording = true;
            gameState.startTime = Date.now();
            gameState.score = 0;
            gameState.combo = 0;
            gameState.accuracy = 100;
            
            document.getElementById('currentChallenge').textContent = challengeType.toUpperCase();
            document.getElementById('recording').textContent = 'ON';
            document.getElementById('recording').style.color = '#ff0000';
            
            document.getElementById('challengeSelector').style.display = 'none';
            document.getElementById('statsPanel').classList.add('active');
            
            initializeDataCollection();
            
            switch (challengeType) {
                case 'aimTrainer':
                    setupAimTrainer();
                    break;
                case 'speedClicker':
                    setupSpeedClicker();
                    break;
                case 'typingRacer':
                    setupTypingRacer();
                    break;
                case 'scrollMaster':
                    setupScrollMaster();
                    break;
                case 'dragRacer':
                    setupDragRacer();
                    break;
                case 'reactionTest':
                    setupReactionTest();
                    break;
                case 'comboChallenge':
                    setupComboChallenge();
                    break;
                case 'endurance':
                    setupEnduranceMode();
                    break;
            }
            
            updateTimer();
        }

        function setupAimTrainer() {
            const gameArea = document.getElementById('gameArea');
            gameArea.innerHTML = '';
            
            // Create targets at different speeds and sizes
            const targetCount = 20;
            let targetsHit = 0;
            
            for (let i = 0; i < targetCount; i++) {
                setTimeout(() => {
                    const target = document.createElement('div');
                    target.className = 'target';
                    target.textContent = i + 1;
                    
                    const size = 30 + Math.random() * 40;
                    target.style.width = target.style.height = size + 'px';
                    target.style.left = Math.random() * (window.innerWidth - size) + 'px';
                    target.style.top = Math.random() * (window.innerHeight - 180) + 100 + 'px';
                    
                    const appearTime = Date.now();
                    
                    target.onclick = () => {
                        const reactionTime = Date.now() - appearTime;
                        recordTargetHit(target, reactionTime);
                        target.classList.add('hit');
                        targetsHit++;
                        
                        setTimeout(() => target.remove(), 500);
                        
                        if (targetsHit >= targetCount) {
                            endChallenge();
                        }
                    };
                    
                    gameArea.appendChild(target);
                    
                    // Remove target after 3 seconds
                    setTimeout(() => {
                        if (target.parentNode) {
                            target.remove();
                            recordTargetMiss();
                        }
                    }, 3000);
                }, i * 800);
            }
        }

        function setupSpeedClicker() {
            const gameArea = document.getElementById('gameArea');
            gameArea.innerHTML = '<div style="color: #00ff00; font-size: 24px; text-align: center; margin-top: 200px;">CLICK AS FAST AS YOU CAN!<br>30 SECONDS</div>';
            
            let clickCount = 0;
            let challengeActive = true;
            
            const clickHandler = (e) => {
                if (challengeActive) {
                    clickCount++;
                    gameState.score = clickCount;
                    updateScore();
                    
                    // Create particle effect
                    createParticles(e.clientX, e.clientY);
                    
                    recordClick(e);
                }
            };
            
            document.addEventListener('click', clickHandler);
            
            setTimeout(() => {
                challengeActive = false;
                document.removeEventListener('click', clickHandler);
                endChallenge();
            }, 30000);
        }

        function setupTypingRacer() {
            const gameArea = document.getElementById('gameArea');
            const texts = [
                "The quick brown fox jumps over the lazy dog",
                "In the beginning was the Word, and the Word was with God",
                "To be or not to be, that is the question",
                "All that glitters is not gold",
                "The pen is mightier than the sword"
            ];
            
            const selectedText = texts[Math.floor(Math.random() * texts.length)];
            
            gameArea.innerHTML = 
                <div class="typing-challenge">
                    <div class="typing-text"></div>
                    <textarea class="typing-input" placeholder="Type the text above as quickly and accurately as possible..."></textarea>
                </div>
            ;
            
            const input = gameArea.querySelector('.typing-input');
            input.focus();
            
            input.addEventListener('input', (e) => {
                recordTyping(e);
                const typed = e.target.value;
                const accuracy = calculateTypingAccuracy(typed, selectedText);
                gameState.accuracy = accuracy;
                updateAccuracy();
                
                if (typed === selectedText) {
                    endChallenge();
                }
            });
        }

        function setupScrollMaster() {
            const gameArea = document.getElementById('gameArea');
            gameArea.innerHTML = '<div class="scroll-track"><div class="scroll-content"></div></div>';
            
            const scrollContent = gameArea.querySelector('.scroll-content');
            const checkpoints = [];
            
            // Create checkpoints at different scroll positions
            for (let i = 0; i < 10; i++) {
                const checkpoint = document.createElement('div');
                checkpoint.className = 'scroll-checkpoint';
                checkpoint.textContent = CP ;
                checkpoint.style.top = (i * 30) + 10 + '%';
                scrollContent.appendChild(checkpoint);
                checkpoints.push(checkpoint);
            }
            
            const scrollTrack = gameArea.querySelector('.scroll-track');
            scrollTrack.addEventListener('scroll', (e) => {
                recordScroll(e);
                
                // Check if checkpoints are reached
                checkpoints.forEach((cp, index) => {
                    const rect = cp.getBoundingClientRect();
                    if (rect.top >= 100 && rect.top <= 200 && !cp.classList.contains('reached')) {
                        cp.classList.add('reached');
                        cp.style.background = 'rgba(0, 255, 0, 0.8)';
                        gameState.score += 100;
                        updateScore();
                        
                        if (index === checkpoints.length - 1) {
                            endChallenge();
                        }
                    }
                });
            });
        }

        function setupDragRacer() {
            const gameArea = document.getElementById('gameArea');
            gameArea.innerHTML = '';
            
            const itemCount = 6;
            const items = [];
            const zones = [];
            
            for (let i = 0; i < itemCount; i++) {
                const item = document.createElement('div');
                item.className = 'drag-item';
                item.textContent = String.fromCharCode(65 + i);
                item.style.left = 50 + (i * 80) + 'px';
                item.style.top = '400px';
                item.draggable = true;
                
                const zone = document.createElement('div');
                zone.className = 'drop-zone';
                zone.textContent = String.fromCharCode(65 + i);
                zone.style.left = 50 + (i * 80) + 'px';
                zone.style.top = '200px';
                
                setupDragEvents(item, zone);
                
                gameArea.appendChild(item);
                gameArea.appendChild(zone);
                
                items.push(item);
                zones.push(zone);
            }
        }

        function setupReactionTest() {
            const gameArea = document.getElementById('gameArea');
            gameArea.innerHTML = '<div style="color: #00ff00; font-size: 24px; text-align: center; margin-top: 200px;">WAIT FOR THE SIGNAL...</div>';
            
            const delay = 2000 + Math.random() * 3000;
            
            setTimeout(() => {
                gameArea.innerHTML = '<div style="color: #ff0000; font-size: 48px; text-align: center; margin-top: 200px;">CLICK NOW!</div>';
                
                const startTime = Date.now();
                
                const clickHandler = () => {
                    const reactionTime = Date.now() - startTime;
                    gameState.score = Math.max(0, 1000 - reactionTime);
                    updateScore();
                    
                    document.removeEventListener('click', clickHandler);
                    
                    gameArea.innerHTML = <div style="color: #00ff00; font-size: 24px; text-align: center; margin-top: 200px;">REACTION TIME: ms</div>;
                    
                    setTimeout(() => endChallenge(), 2000);
                };
                
                document.addEventListener('click', clickHandler);
            }, delay);
        }

        function setupComboChallenge() {
            const gameArea = document.getElementById('gameArea');
            gameArea.innerHTML = '';
            
            const sequence = [];
            const actions = ['click', 'drag', 'type', 'scroll'];
            
            for (let i = 0; i < 10; i++) {
                sequence.push(actions[Math.floor(Math.random() * actions.length)]);
            }
            
            let currentStep = 0;
            
            function nextStep() {
                if (currentStep >= sequence.length) {
                    endChallenge();
                    return;
                }
                
                const action = sequence[currentStep];
                gameArea.innerHTML = <div style="color: #00ff00; font-size: 24px; text-align: center; margin-top: 200px;">COMBO : </div>;
                
                switch (action) {
                    case 'click':
                        gameArea.innerHTML += '<div class="target" style="left: 50%; top: 50%; transform: translate(-50%, -50%); width: 50px; height: 50px;"></div>';
                        gameArea.querySelector('.target').onclick = () => {
                            currentStep++;
                            gameState.combo++;
                            updateCombo();
                            nextStep();
                        };
                        break;
                    case 'drag':
                        gameArea.innerHTML += '<div class="drag-item" style="left: 40%; top: 50%; transform: translate(-50%, -50%);">DRAG</div>';
                        gameArea.innerHTML += '<div class="drop-zone" style="left: 60%; top: 50%; transform: translate(-50%, -50%);">HERE</div>';
                        // Setup drag events
                        break;
                    case 'type':
                        gameArea.innerHTML += '<input type="text" placeholder="Type anything..." style="margin-top: 50px; padding: 10px; width: 200px;">';
                        gameArea.querySelector('input').addEventListener('input', () => {
                            currentStep++;
                            gameState.combo++;
                            updateCombo();
                            nextStep();
                        });
                        break;
                    case 'scroll':
                        gameArea.innerHTML += '<div style="height: 200px; overflow-y: auto; border: 2px solid #00ff00; margin-top: 50px;"><div style="height: 400px; padding: 20px;">Scroll down to continue...</div></div>';
                        gameArea.querySelector('div div').addEventListener('scroll', () => {
                            currentStep++;
                            gameState.combo++;
                            updateCombo();
                            nextStep();
                        });
                        break;
                }
            }
            
            nextStep();
        }

        function setupEnduranceMode() {
            const gameArea = document.getElementById('gameArea');
            gameArea.innerHTML = '<div style="color: #00ff00; font-size: 24px; text-align: center; margin-top: 200px;">ENDURANCE MODE: 5 MINUTES<br>Mix of all challenges!</div>';
            
            const challenges = ['aimTrainer', 'speedClicker', 'typingRacer', 'scrollMaster', 'dragRacer'];
            let currentChallengeIndex = 0;
            
            function rotateChallenge() {
                if (Date.now() - gameState.startTime > 300000) { // 5 minutes
                    endChallenge();
                    return;
                }
                
                const challenge = challenges[currentChallengeIndex % challenges.length];
                currentChallengeIndex++;
                
                // Mini version of each challenge
                switch (challenge) {
                    case 'aimTrainer':
                        setupMiniAimTrainer();
                        break;
                    case 'speedClicker':
                        setupMiniSpeedClicker();
                        break;
                    // ... implement mini versions
                }
                
                setTimeout(rotateChallenge, 30000); // 30 seconds per mini challenge
            }
            
            setTimeout(rotateChallenge, 3000);
        }

        function initializeDataCollection() {
            document.addEventListener('mousemove', recordMouseMove);
            document.addEventListener('mousedown', recordMouseDown);
            document.addEventListener('mouseup', recordMouseUp);
            document.addEventListener('keydown', recordKeyDown);
            document.addEventListener('keyup', recordKeyUp);
            document.addEventListener('wheel', recordWheel);
        }

        function recordMouseMove(e) {
            if (!gameState.recording) return;
            
            const now = Date.now();
            const event = {
                type: 'mousemove',
                timestamp: now - gameState.startTime,
                x: e.clientX,
                y: e.clientY,
                movementX: e.movementX,
                movementY: e.movementY,
                pressure: e.pressure || 0
            };
            
            gameState.sessionData.mouseEvents.push(event);
            
            // Update mini-map
            updateMiniMap(e.clientX, e.clientY);
            
            // Create mouse trail
            createMouseTrail(e.clientX, e.clientY);
            
            // Calculate mouse speed
            if (lastMouseTime > 0) {
                const distance = Math.sqrt(
                    Math.pow(e.clientX - lastMousePos.x, 2) + 
                    Math.pow(e.clientY - lastMousePos.y, 2)
                );
                const timeDiff = now - lastMouseTime;
                const speed = timeDiff > 0 ? (distance / timeDiff) * 1000 : 0;
                
                document.getElementById('mouseSpeed').textContent = speed.toFixed(1) + ' px/s';
            }
            
            lastMousePos = { x: e.clientX, y: e.clientY };
            lastMouseTime = now;
        }

        function recordMouseDown(e) {
            if (!gameState.recording) return;
            
            gameState.sessionData.mouseEvents.push({
                type: 'mousedown',
                timestamp: Date.now() - gameState.startTime,
                x: e.clientX,
                y: e.clientY,
                button: e.button
            });
        }

        function recordMouseUp(e) {
            if (!gameState.recording) return;
            
            gameState.sessionData.mouseEvents.push({
                type: 'mouseup',
                timestamp: Date.now() - gameState.startTime,
                x: e.clientX,
                y: e.clientY,
                button: e.button
            });
        }

        function recordKeyDown(e) {
            if (!gameState.recording) return;
            
            gameState.sessionData.keyEvents.push({
                type: 'keydown',
                timestamp: Date.now() - gameState.startTime,
                key: e.key,
                code: e.code,
                ctrlKey: e.ctrlKey,
                shiftKey: e.shiftKey,
                altKey: e.altKey,
                metaKey: e.metaKey
            });
        }

        function recordKeyUp(e) {
            if (!gameState.recording) return;
            
            gameState.sessionData.keyEvents.push({
                type: 'keyup',
                timestamp: Date.now() - gameState.startTime,
                key: e.key,
                code: e.code
            });
        }

        function recordWheel(e) {
            if (!gameState.recording) return;
            
            gameState.sessionData.scrollEvents.push({
                type: 'wheel',
                timestamp: Date.now() - gameState.startTime,
                deltaX: e.deltaX,
                deltaY: e.deltaY,
                deltaZ: e.deltaZ,
                deltaMode: e.deltaMode
            });
        }

        function recordScroll(e) {
            if (!gameState.recording) return;
            
            gameState.sessionData.scrollEvents.push({
                type: 'scroll',
                timestamp: Date.now() - gameState.startTime,
                scrollTop: e.target.scrollTop,
                scrollLeft: e.target.scrollLeft
            });
        }

        function recordTargetHit(target, reactionTime) {
            gameState.score += 100;
            gameState.combo++;
            
            const achievement = {
                type: 'target_hit',
                timestamp: Date.now() - gameState.startTime,
                targetId: target.textContent,
                reactionTime: reactionTime,
                score: 100
            };
            
            gameState.sessionData.challenges.push(achievement);
            
            updateScore();
            updateCombo();
            
            // Show achievement
            showAchievement('TARGET HIT! +100 pts');
        }

        function recordTargetMiss() {
            gameState.combo = 0;
            gameState.accuracy = Math.max(0, gameState.accuracy - 5);
            
            updateCombo();
            updateAccuracy();
        }

        function recordClick(e) {
            gameState.sessionData.mouseEvents.push({
                type: 'click',
                timestamp: Date.now() - gameState.startTime,
                x: e.clientX,
                y: e.clientY,
                button: e.button
            });
        }

        function recordTyping(e) {
            const typingData = {
                type: 'typing',
                timestamp: Date.now() - gameState.startTime,
                text: e.target.value,
                length: e.target.value.length
            };
            
            gameState.sessionData.keyEvents.push(typingData);
        }

        function calculateTypingAccuracy(typed, target) {
            let correct = 0;
            const minLength = Math.min(typed.length, target.length);
            
            for (let i = 0; i < minLength; i++) {
                if (typed[i] === target[i]) correct++;
            }
            
            return minLength > 0 ? Math.round((correct / minLength) * 100) : 100;
        }

        function setupDragEvents(item, zone) {
            item.addEventListener('dragstart', (e) => {
                recordDragStart(e, item);
            });
            
            item.addEventListener('drag', (e) => {
                recordDrag(e, item);
            });
            
            zone.addEventListener('dragover', (e) => {
                e.preventDefault();
                zone.classList.add('active');
            });
            
            zone.addEventListener('dragleave', (e) => {
                zone.classList.remove('active');
            });
            
            zone.addEventListener('drop', (e) => {
                e.preventDefault();
                zone.classList.remove('active');
                
                if (item.textContent === zone.textContent) {
                    recordDragSuccess(e, item, zone);
                    item.style.left = zone.style.left;
                    item.style.top = zone.style.top;
                    gameState.score += 200;
                    updateScore();
                    showAchievement('PERFECT DROP! +200 pts');
                }
            });
        }

        function recordDragStart(e, item) {
            gameState.sessionData.mouseEvents.push({
                type: 'drag_start',
                timestamp: Date.now() - gameState.startTime,
                itemId: item.textContent,
                x: e.clientX,
                y: e.clientY
            });
        }

        function recordDrag(e, item) {
            gameState.sessionData.mouseEvents.push({
                type: 'drag',
                timestamp: Date.now() - gameState.startTime,
                itemId: item.textContent,
                x: e.clientX,
                y: e.clientY
            });
        }

        function recordDragSuccess(e, item, zone) {
            gameState.sessionData.mouseEvents.push({
                type: 'drag_success',
                timestamp: Date.now() - gameState.startTime,
                itemId: item.textContent,
                zoneId: zone.textContent,
                x: e.clientX,
                y: e.clientY
            });
        }

        function createMouseTrail(x, y) {
            const trail = document.createElement('div');
            trail.className = 'path-tracer';
            trail.style.left = x + 'px';
            trail.style.top = y + 'px';
            
            document.body.appendChild(trail);
            
            setTimeout(() => {
                trail.style.opacity = '0';
                setTimeout(() => trail.remove(), 1000);
            }, 100);
        }

        function createParticles(x, y) {
            for (let i = 0; i < 10; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = x + 'px';
                particle.style.top = y + 'px';
                
                const angle = (Math.PI * 2 * i) / 10;
                const velocity = 2 + Math.random() * 3;
                
                particle.style.transform = 	ranslate(px, px);
                particle.style.opacity = '0';
                
                document.body.appendChild(particle);
                
                setTimeout(() => particle.remove(), 1000);
            }
        }

        function updateMiniMap(x, y) {
            const miniMap = document.getElementById('miniMap');
            const dot = document.createElement('div');
            dot.className = 'mini-map-dot';
            dot.style.left = (x / window.innerWidth) * 100 + '%';
            dot.style.top = (y / window.innerHeight) * 100 + '%';
            
            miniMap.appendChild(dot);
            
            setTimeout(() => {
                dot.style.opacity = '0';
                setTimeout(() => dot.remove(), 1000);
            }, 2000);
        }

        function showAchievement(text) {
            const achievement = document.createElement('div');
            achievement.className = 'achievement';
            achievement.textContent = text;
            
            document.body.appendChild(achievement);
            
            setTimeout(() => achievement.remove(), 3000);
        }

        function updateScore() {
            document.getElementById('score').textContent = gameState.score;
        }

        function updateCombo() {
            document.getElementById('combo').textContent = gameState.combo;
            
            const comboFill = document.getElementById('comboFill');
            const comboPercent = Math.min(100, (gameState.combo / 10) * 100);
            comboFill.style.width = comboPercent + '%';
        }

        function updateAccuracy() {
            document.getElementById('accuracy').textContent = gameState.accuracy + '%';
        }

        function updateTimer() {
            if (!gameState.recording) return;
            
            const elapsed = Date.now() - gameState.startTime;
            const minutes = Math.floor(elapsed / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);
            
            document.getElementById('timer').textContent = 
                ${minutes.toString().padStart(2, '0')}:;
            
            setTimeout(updateTimer, 1000);
        }

        function endChallenge() {
            gameState.recording = false;
            
            document.getElementById('recording').textContent = 'OFF';
            document.getElementById('recording').style.color = '#00ff00';
            
            const endTime = Date.now();
            const duration = endTime - gameState.startTime;
            
            gameState.sessionData.challenges.push({
                type: 'challenge_complete',
                challenge: gameState.currentChallenge,
                duration: duration,
                finalScore: gameState.score,
                finalCombo: gameState.combo,
                finalAccuracy: gameState.accuracy
            });
            
            showChallengeComplete(duration);
            
            console.log('Challenge Data:', gameState.sessionData);
            // Here you would typically send the data to your server
        }

        function showChallengeComplete(duration) {
            const complete = document.createElement('div');
            complete.className = 'challenge-complete';
            complete.innerHTML = `
                <h2>CHALLENGE COMPLETE!</h2>
                <div class="stats">
                    <div>Final Score: ${gameState.score}</div>
                    <div>Max Combo: ${gameState.combo}</div>
                    <div>Accuracy: ${gameState.accuracy}%</div>
                    <div>Duration: ${(duration / 1000).toFixed(2)}s</div>
                </div>
                <button class="restart-btn" onclick="restartGame()">PLAY AGAIN</button>
            `;
            
            document.body.appendChild(complete);
        }

        function restartGame() {
            document.querySelector('.challenge-complete').remove();
            document.getElementById('statsPanel').classList.remove('active');
            document.getElementById('challengeSelector').style.display = 'flex';
            document.getElementById('gameArea').innerHTML = '';
            document.getElementById('gameArea').appendChild(document.getElementById('challengeSelector'));
            
            // Reset game state
            gameState = {
                recording: false,
                currentChallenge: null,
                startTime: null,
                score: 0,
                combo: 0,
                accuracy: 100,
                level: 1,
                sessionData: {
                    mouseEvents: [],
                    keyEvents: [],
                    scrollEvents: [],
                    challenges: []
                }
            };
            
            updateScore();
            updateCombo();
            updateAccuracy();
            
            document.getElementById('currentChallenge').textContent = 'SELECT';
            document.getElementById('timer').textContent = '00:00';
        }

        // Initialize the game
        document.addEventListener('DOMContentLoaded', () => {
            updateScore();
            updateCombo();
            updateAccuracy();
        });
    </script>
</body>
</html>
