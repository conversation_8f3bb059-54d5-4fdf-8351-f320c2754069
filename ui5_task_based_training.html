<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Task-Based Training Interface</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            overflow: hidden;
        }

        .container {
            display: flex;
            height: 100vh;
        }

        .sidebar {
            width: 300px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px;
            overflow-y: auto;
            border-right: 1px solid rgba(0, 0, 0, 0.1);
        }

        .main-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .task-header {
            background: rgba(255, 255, 255, 0.9);
            padding: 20px;
            border-bottom: 2px solid #667eea;
        }

        .task-playground {
            flex: 1;
            position: relative;
            background: rgba(255, 255, 255, 0.1);
            overflow: hidden;
        }

        .task-selector {
            margin-bottom: 20px;
        }

        .task-btn {
            display: block;
            width: 100%;
            padding: 15px;
            margin: 10px 0;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .task-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        }

        .task-btn.active {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
        }

        .recording-controls {
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .record-btn {
            width: 100%;
            padding: 15px;
            background: #ff4757;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .record-btn.recording {
            background: #2ed573;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .metrics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 10px;
        }

        .metric {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            color: white;
        }

        .task-instructions {
            background: rgba(255, 255, 255, 0.9);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .progress-bar {
            width: 100%;
            height: 10px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 5px;
            overflow: hidden;
            margin-top: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width 0.3s ease;
        }

        /* Task-specific elements */
        .target-circle {
            position: absolute;
            width: 50px;
            height: 50px;
            background: radial-gradient(circle, #ff6b6b, #ee5a24);
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 3px solid white;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .target-circle:hover {
            transform: scale(1.2);
            box-shadow: 0 6px 30px rgba(0, 0, 0, 0.4);
        }

        .drag-item {
            position: absolute;
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, #3742fa, #2f3542);
            border-radius: 10px;
            cursor: grab;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .drag-item:active {
            cursor: grabbing;
        }

        .drop-zone {
            position: absolute;
            width: 100px;
            height: 100px;
            border: 3px dashed #667eea;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #667eea;
            font-weight: bold;
            background: rgba(255, 255, 255, 0.1);
        }

        .typing-area {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80%;
            max-width: 600px;
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
        }

        .typing-prompt {
            font-size: 18px;
            margin-bottom: 20px;
            color: #333;
            line-height: 1.6;
        }

        .typing-input {
            width: 100%;
            min-height: 200px;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 16px;
            resize: vertical;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .typing-input:focus {
            outline: none;
            border-color: #667eea;
        }

        .scroll-content {
            height: 100%;
            overflow-y: auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
        }

        .scroll-section {
            margin-bottom: 50px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        }

        .path-challenge {
            position: relative;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23ddd" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        }

        .path-line {
            position: absolute;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
            opacity: 0.7;
        }

        .checkpoint {
            position: absolute;
            width: 20px;
            height: 20px;
            background: #ff6b6b;
            border-radius: 50%;
            border: 3px solid white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .data-visualization {
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
        }

        .data-row {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
            font-family: monospace;
        }

        .hidden {
            display: none;
        }

        .task-timer {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
            text-align: center;
            margin-bottom: 10px;
        }

        .difficulty-selector {
            margin-bottom: 20px;
        }

        .difficulty-btn {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
        }

        .difficulty-btn.easy { background: #2ed573; color: white; }
        .difficulty-btn.medium { background: #ffa502; color: white; }
        .difficulty-btn.hard { background: #ff4757; color: white; }
        .difficulty-btn.expert { background: #5f27cd; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <h2>Human Interaction Recorder</h2>
            <div class="task-selector">
                <h3>Training Tasks</h3>
                <button class="task-btn" onclick="selectTask('precision')">Precision Clicking</button>
                <button class="task-btn" onclick="selectTask('drag')">Drag & Drop Patterns</button>
                <button class="task-btn" onclick="selectTask('typing')">Typing Behavior</button>
                <button class="task-btn" onclick="selectTask('scroll')">Scrolling Patterns</button>
                <button class="task-btn" onclick="selectTask('path')">Mouse Path Following</button>
                <button class="task-btn" onclick="selectTask('complex')">Complex Interactions</button>
            </div>

            <div class="difficulty-selector">
                <h4>Difficulty Level</h4>
                <button class="difficulty-btn easy" onclick="setDifficulty('easy')">Easy</button>
                <button class="difficulty-btn medium" onclick="setDifficulty('medium')">Medium</button>
                <button class="difficulty-btn hard" onclick="setDifficulty('hard')">Hard</button>
                <button class="difficulty-btn expert" onclick="setDifficulty('expert')">Expert</button>
            </div>

            <div class="recording-controls">
                <button class="record-btn" onclick="toggleRecording()">Start Recording</button>
                <div class="task-timer">00:00</div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 0%"></div>
                </div>
                <div class="metrics">
                    <div class="metric">
                        <div>Mouse Events</div>
                        <div id="mouseEvents">0</div>
                    </div>
                    <div class="metric">
                        <div>Scroll Events</div>
                        <div id="scrollEvents">0</div>
                    </div>
                    <div class="metric">
                        <div>Key Events</div>
                        <div id="keyEvents">0</div>
                    </div>
                    <div class="metric">
                        <div>Accuracy</div>
                        <div id="accuracy">0%</div>
                    </div>
                </div>
            </div>

            <div class="data-visualization">
                <h4>Live Data Stream</h4>
                <div id="dataStream">
                    <div class="data-row">
                        <span>Velocity:</span>
                        <span id="mouseVelocity">0 px/s</span>
                    </div>
                    <div class="data-row">
                        <span>Acceleration:</span>
                        <span id="mouseAcceleration">0 px/s�</span>
                    </div>
                    <div class="data-row">
                        <span>Path Deviation:</span>
                        <span id="pathDeviation">0 px</span>
                    </div>
                    <div class="data-row">
                        <span>Typing Speed:</span>
                        <span id="typingSpeed">0 WPM</span>
                    </div>
                    <div class="data-row">
                        <span>Scroll Velocity:</span>
                        <span id="scrollVelocity">0 px/s</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="main-area">
            <div class="task-header">
                <h2 id="taskTitle">Select a Task to Begin</h2>
                <p id="taskDescription">Choose from the training tasks on the left to start recording human interaction data.</p>
            </div>

            <div class="task-playground" id="playground">
                <!-- Tasks will be dynamically loaded here -->
            </div>
        </div>
    </div>

    <script>
        let isRecording = false;
        let currentTask = null;
        let difficulty = 'medium';
        let startTime = null;
        let taskData = {
            mouseEvents: [],
            scrollEvents: [],
            keyEvents: [],
            taskMetrics: {}
        };

        // Task configurations
        const tasks = {
            precision: {
                title: "Precision Clicking Challenge",
                description: "Click on targets that appear randomly. Measures accuracy, reaction time, and movement patterns.",
                setup: setupPrecisionTask
            },
            drag: {
                title: "Drag & Drop Patterns",
                description: "Drag items to specific zones. Captures dragging behavior, acceleration, and path optimization.",
                setup: setupDragTask
            },
            typing: {
                title: "Typing Behavior Analysis",
                description: "Type various text patterns. Records keystroke timing, pauses, corrections, and typing rhythm.",
                setup: setupTypingTask
            },
            scroll: {
                title: "Scrolling Pattern Recognition",
                description: "Navigate through content using various scrolling techniques. Captures scroll velocity and patterns.",
                setup: setupScrollTask
            },
            path: {
                title: "Mouse Path Following",
                description: "Follow predefined paths with your mouse. Measures path adherence and movement smoothness.",
                setup: setupPathTask
            },
            complex: {
                title: "Complex Multi-Modal Interactions",
                description: "Combination of clicking, dragging, typing, and scrolling in realistic scenarios.",
                setup: setupComplexTask
            }
        };

        function selectTask(taskName) {
            currentTask = taskName;
            document.querySelectorAll('.task-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            const task = tasks[taskName];
            document.getElementById('taskTitle').textContent = task.title;
            document.getElementById('taskDescription').textContent = task.description;
            
            task.setup();
        }

        function setDifficulty(level) {
            difficulty = level;
            document.querySelectorAll('.difficulty-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            if (currentTask) {
                tasks[currentTask].setup();
            }
        }

        function setupPrecisionTask() {
            const playground = document.getElementById('playground');
            playground.innerHTML = '';
            
            const targetCount = { easy: 10, medium: 20, hard: 35, expert: 50 }[difficulty];
            const targetSize = { easy: 60, medium: 40, hard: 25, expert: 15 }[difficulty];
            
            for (let i = 0; i < targetCount; i++) {
                setTimeout(() => {
                    const target = document.createElement('div');
                    target.className = 'target-circle';
                    target.style.width = target.style.height = targetSize + 'px';
                    target.style.left = Math.random() * (playground.offsetWidth - targetSize) + 'px';
                    target.style.top = Math.random() * (playground.offsetHeight - targetSize) + 'px';
                    
                    target.onclick = () => {
                        recordTargetHit(target);
                        target.remove();
                    };
                    
                    playground.appendChild(target);
                    
                    setTimeout(() => {
                        if (target.parentNode) {
                            target.remove();
                        }
                    }, { easy: 5000, medium: 3000, hard: 2000, expert: 1000 }[difficulty]);
                }, i * 1000);
            }
        }

        function setupDragTask() {
            const playground = document.getElementById('playground');
            playground.innerHTML = '';
            
            const itemCount = { easy: 3, medium: 5, hard: 8, expert: 12 }[difficulty];
            
            for (let i = 0; i < itemCount; i++) {
                const item = document.createElement('div');
                item.className = 'drag-item';
                item.textContent = i + 1;
                item.style.left = Math.random() * (playground.offsetWidth - 60) + 'px';
                item.style.top = Math.random() * (playground.offsetHeight - 60) + 'px';
                item.draggable = true;
                
                const zone = document.createElement('div');
                zone.className = 'drop-zone';
                zone.textContent = i + 1;
                zone.style.right = 20 + (i * 120) + 'px';
                zone.style.top = 20 + 'px';
                
                playground.appendChild(item);
                playground.appendChild(zone);
                
                setupDragEvents(item, zone);
            }
        }

        function setupTypingTask() {
            const playground = document.getElementById('playground');
            const prompts = {
                easy: "Type this simple sentence to test your typing patterns.",
                medium: "The quick brown fox jumps over the lazy dog. This sentence contains every letter of the alphabet and will help analyze your typing rhythm and accuracy.",
                hard: "In a hole in the ground there lived a hobbit. Not a nasty, dirty, wet hole, filled with the ends of worms and an oozy smell, nor yet a dry, bare, sandy hole with nothing in it to sit down on or to eat: it was a hobbit-hole, and that means comfort.",
                expert: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum."
            };
            
            playground.innerHTML = 
                <div class="typing-area">
                    <div class="typing-prompt"></div>
                    <textarea class="typing-input" placeholder="Start typing here..." oninput="recordTyping(event)"></textarea>
                </div>
            ;
        }

        function setupScrollTask() {
            const playground = document.getElementById('playground');
            const sectionCount = { easy: 5, medium: 10, hard: 20, expert: 30 }[difficulty];
            
            let content = '<div class="scroll-content">';
            for (let i = 0; i < sectionCount; i++) {
                content += 
                    <div class="scroll-section">
                        <h3>Section </h3>
                        <p>This is section  of the scrolling challenge. The content varies in length and complexity to test different scrolling patterns. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
                        
                    </div>
                ;
            }
            content += '</div>';
            
            playground.innerHTML = content;
            
            const scrollArea = playground.querySelector('.scroll-content');
            scrollArea.addEventListener('scroll', recordScroll);
        }

        function setupPathTask() {
            const playground = document.getElementById('playground');
            playground.innerHTML = '<div class="path-challenge"></div>';
            
            const pathComplexity = { easy: 3, medium: 5, hard: 8, expert: 12 }[difficulty];
            const paths = generatePaths(pathComplexity);
            
            paths.forEach((path, index) => {
                const pathElement = document.createElement('div');
                pathElement.className = 'path-line';
                pathElement.style.left = path.x + 'px';
                pathElement.style.top = path.y + 'px';
                pathElement.style.width = path.width + 'px';
                pathElement.style.transform = 
otate(deg);
                playground.appendChild(pathElement);
                
                if (index === 0 || index === paths.length - 1) {
                    const checkpoint = document.createElement('div');
                    checkpoint.className = 'checkpoint';
                    checkpoint.style.left = (index === 0 ? path.x : path.x + path.width) + 'px';
                    checkpoint.style.top = path.y + 'px';
                    playground.appendChild(checkpoint);
                }
            });
        }

        function setupComplexTask() {
            const playground = document.getElementById('playground');
            playground.innerHTML = 
                '<div style="position: relative; height: 100%;">' +
                    '<div style="position: absolute; top: 20px; left: 20px; right: 20px; height: 200px; background: rgba(255,255,255,0.9); border-radius: 10px; padding: 20px;">' +
                        '<h3>Complex Task: Website Simulation</h3>' +
                        '<p>Complete the following actions in order:</p>' +
                        '<ol>' +
                            '<li>Scroll to find the "Submit" button</li>' +
                            '<li>Fill out the form below</li>' +
                            '<li>Drag the slider to set a value</li>' +
                            '<li>Click the submit button</li>' +
                        '</ol>' +
                    '</div>' +
                    '<div style="position: absolute; top: 250px; left: 20px; right: 20px; bottom: 20px; background: rgba(255,255,255,0.9); border-radius: 10px; padding: 20px; overflow-y: auto;">' +
                        '<form>' +
                            '<input type="text" placeholder="Name" style="width: 100%; padding: 10px; margin: 10px 0; border: 1px solid #ddd; border-radius: 5px;">' +
                            '<input type="email" placeholder="Email" style="width: 100%; padding: 10px; margin: 10px 0; border: 1px solid #ddd; border-radius: 5px;">' +
                            '<textarea placeholder="Message" style="width: 100%; height: 100px; padding: 10px; margin: 10px 0; border: 1px solid #ddd; border-radius: 5px;"></textarea>' +
                            '<div style="margin: 20px 0;">' +
                                '<label>Satisfaction Level:</label>' +
                                '<input type="range" min="1" max="10" style="width: 100%; margin: 10px 0;">' +
                            '</div>' +
                            '<button type="submit" style="background: #667eea; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;">Submit</button>' +
                        '</form>' +
                    '</div>' +
                '</div>';
        }

        function toggleRecording() {
            isRecording = !isRecording;
            const btn = document.querySelector('.record-btn');
            
            if (isRecording) {
                btn.textContent = 'Stop Recording';
                btn.classList.add('recording');
                startTime = Date.now();
                startDataCollection();
            } else {
                btn.textContent = 'Start Recording';
                btn.classList.remove('recording');
                stopDataCollection();
            }
        }

        function startDataCollection() {
            taskData = {
                mouseEvents: [],
                scrollEvents: [],
                keyEvents: [],
                taskMetrics: {
                    task: currentTask,
                    difficulty: difficulty,
                    startTime: startTime
                }
            };
            
            document.addEventListener('mousemove', recordMouseMove);
            document.addEventListener('mousedown', recordMouseDown);
            document.addEventListener('mouseup', recordMouseUp);
            document.addEventListener('keydown', recordKeyDown);
            document.addEventListener('keyup', recordKeyUp);
            
            updateTimer();
        }

        function stopDataCollection() {
            document.removeEventListener('mousemove', recordMouseMove);
            document.removeEventListener('mousedown', recordMouseDown);
            document.removeEventListener('mouseup', recordMouseUp);
            document.removeEventListener('keydown', recordKeyDown);
            document.removeEventListener('keyup', recordKeyUp);
            
            taskData.taskMetrics.endTime = Date.now();
            taskData.taskMetrics.duration = taskData.taskMetrics.endTime - taskData.taskMetrics.startTime;
            
            console.log('Collected Data:', taskData);
            // Here you would typically send the data to your server
        }

        function recordMouseMove(e) {
            if (!isRecording) return;
            
            const event = {
                type: 'mousemove',
                timestamp: Date.now() - startTime,
                x: e.clientX,
                y: e.clientY,
                pressure: e.pressure || 0,
                movementX: e.movementX,
                movementY: e.movementY
            };
            
            taskData.mouseEvents.push(event);
            updateMetrics();
        }

        function recordMouseDown(e) {
            if (!isRecording) return;
            
            taskData.mouseEvents.push({
                type: 'mousedown',
                timestamp: Date.now() - startTime,
                x: e.clientX,
                y: e.clientY,
                button: e.button
            });
        }

        function recordMouseUp(e) {
            if (!isRecording) return;
            
            taskData.mouseEvents.push({
                type: 'mouseup',
                timestamp: Date.now() - startTime,
                x: e.clientX,
                y: e.clientY,
                button: e.button
            });
        }

        function recordKeyDown(e) {
            if (!isRecording) return;
            
            taskData.keyEvents.push({
                type: 'keydown',
                timestamp: Date.now() - startTime,
                key: e.key,
                code: e.code,
                ctrlKey: e.ctrlKey,
                shiftKey: e.shiftKey,
                altKey: e.altKey
            });
        }

        function recordKeyUp(e) {
            if (!isRecording) return;
            
            taskData.keyEvents.push({
                type: 'keyup',
                timestamp: Date.now() - startTime,
                key: e.key,
                code: e.code
            });
        }

        function recordScroll(e) {
            if (!isRecording) return;
            
            taskData.scrollEvents.push({
                type: 'scroll',
                timestamp: Date.now() - startTime,
                scrollTop: e.target.scrollTop,
                scrollLeft: e.target.scrollLeft,
                deltaY: e.deltaY || 0,
                deltaX: e.deltaX || 0
            });
        }

        function updateMetrics() {
            document.getElementById('mouseEvents').textContent = taskData.mouseEvents.length;
            document.getElementById('scrollEvents').textContent = taskData.scrollEvents.length;
            document.getElementById('keyEvents').textContent = taskData.keyEvents.length;
            
            // Calculate and display advanced metrics
            calculateAdvancedMetrics();
        }

        function calculateAdvancedMetrics() {
            const mouseEvents = taskData.mouseEvents.filter(e => e.type === 'mousemove');
            
            if (mouseEvents.length > 1) {
                const lastEvent = mouseEvents[mouseEvents.length - 1];
                const prevEvent = mouseEvents[mouseEvents.length - 2];
                
                const distance = Math.sqrt(
                    Math.pow(lastEvent.x - prevEvent.x, 2) + 
                    Math.pow(lastEvent.y - prevEvent.y, 2)
                );
                
                const timeDiff = lastEvent.timestamp - prevEvent.timestamp;
                const velocity = timeDiff > 0 ? (distance / timeDiff) * 1000 : 0;
                
                document.getElementById('mouseVelocity').textContent = velocity.toFixed(2) + ' px/s';
            }
        }

        function updateTimer() {
            if (!isRecording) return;
            
            const elapsed = Date.now() - startTime;
            const minutes = Math.floor(elapsed / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);
            
            document.querySelector('.task-timer').textContent = 
                minutes.toString().padStart(2, '0') + ':' + seconds.toString().padStart(2, '0');
            
            setTimeout(updateTimer, 1000);
        }

        function generatePaths(complexity) {
            // Generate curved paths for mouse following
            const paths = [];
            const width = 800;
            const height = 600;
            
            for (let i = 0; i < complexity; i++) {
                paths.push({
                    x: Math.random() * width,
                    y: Math.random() * height,
                    width: 50 + Math.random() * 100,
                    angle: Math.random() * 360
                });
            }
            
            return paths;
        }

        function recordTargetHit(target) {
            if (!isRecording) return;
            
            taskData.mouseEvents.push({
                type: 'target_hit',
                timestamp: Date.now() - startTime,
                targetId: target.id,
                accuracy: 'hit'
            });
        }

        function setupDragEvents(item, zone) {
            item.addEventListener('dragstart', (e) => {
                if (isRecording) {
                    taskData.mouseEvents.push({
                        type: 'drag_start',
                        timestamp: Date.now() - startTime,
                        itemId: item.textContent,
                        x: e.clientX,
                        y: e.clientY
                    });
                }
            });
            
            zone.addEventListener('dragover', (e) => {
                e.preventDefault();
            });
            
            zone.addEventListener('drop', (e) => {
                e.preventDefault();
                if (isRecording) {
                    taskData.mouseEvents.push({
                        type: 'drag_drop',
                        timestamp: Date.now() - startTime,
                        itemId: item.textContent,
                        zoneId: zone.textContent,
                        x: e.clientX,
                        y: e.clientY
                    });
                }
            });
        }

        function recordTyping(e) {
            if (!isRecording) return;
            
            const text = e.target.value;
            const words = text.trim().split(/\s+/).length;
            const timeElapsed = (Date.now() - startTime) / 1000 / 60; // minutes
            const wpm = timeElapsed > 0 ? Math.round(words / timeElapsed) : 0;
            
            document.getElementById('typingSpeed').textContent = wpm + ' WPM';
        }

        // Initialize
        selectTask('precision');
    </script>
</body>
</html>
