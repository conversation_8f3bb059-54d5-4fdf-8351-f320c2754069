<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Human Interaction Recorder - Gaming Analytics</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Orbitron', 'Roboto Mono', monospace;
            background: linear-gradient(45deg, #0f0f0f 0%, #1a1a2e 50%, #16213e 100%);
            color: #00ff88;
            overflow-x: hidden;
            min-height: 100vh;
        }

        .container {
            padding: 20px;
            max-width: 1600px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            position: relative;
        }

        .header h1 {
            font-size: 3em;
            text-shadow: 0 0 20px #00ff88;
            margin-bottom: 10px;
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { text-shadow: 0 0 20px #00ff88, 0 0 30px #00ff88, 0 0 40px #00ff88; }
            to { text-shadow: 0 0 10px #00ff88, 0 0 20px #00ff88, 0 0 30px #00ff88; }
        }

        .tagline {
            font-size: 1.2em;
            color: #00ccaa;
            opacity: 0.8;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 350px 1fr 300px;
            gap: 20px;
            margin-bottom: 20px;
        }

        .panel {
            background: linear-gradient(135deg, rgba(0, 255, 136, 0.1) 0%, rgba(0, 255, 136, 0.05) 100%);
            border: 2px solid #00ff88;
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 0 30px rgba(0, 255, 136, 0.3);
        }

        .panel-title {
            font-size: 1.4em;
            margin-bottom: 20px;
            text-align: center;
            color: #00ff88;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .control-panel {
            height: fit-content;
        }

        .record-control {
            text-align: center;
            margin-bottom: 30px;
        }

        .record-btn {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 4px solid #00ff88;
            background: radial-gradient(circle, #001122 0%, #003344 100%);
            color: #00ff88;
            font-size: 1.2em;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .record-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 0 40px rgba(0, 255, 136, 0.5);
        }

        .record-btn.recording {
            border-color: #ff4444;
            color: #ff4444;
            animation: recordPulse 1.5s infinite;
        }

        @keyframes recordPulse {
            0%, 100% { box-shadow: 0 0 20px rgba(255, 68, 68, 0.5); }
            50% { box-shadow: 0 0 40px rgba(255, 68, 68, 0.8); }
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(0, 255, 136, 0.1);
            border: 1px solid #00ff88;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }

        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #00ff88;
            display: block;
        }

        .stat-label {
            font-size: 0.8em;
            color: #00ccaa;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .settings-section {
            margin-bottom: 20px;
        }

        .setting-group {
            margin-bottom: 15px;
        }

        .setting-label {
            display: block;
            margin-bottom: 5px;
            color: #00ccaa;
            font-size: 0.9em;
            text-transform: uppercase;
        }

        .setting-input {
            width: 100%;
            padding: 10px;
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid #00ff88;
            border-radius: 5px;
            color: #00ff88;
            font-family: 'Orbitron', monospace;
        }

        .setting-input:focus {
            outline: none;
            box-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
        }

        .checkbox-wrapper {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }

        .cyber-checkbox {
            width: 20px;
            height: 20px;
            appearance: none;
            border: 2px solid #00ff88;
            background: transparent;
            cursor: pointer;
            position: relative;
        }

        .cyber-checkbox:checked {
            background: #00ff88;
        }

        .cyber-checkbox:checked::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #000;
            font-weight: bold;
        }

        .analytics-panel {
            height: 600px;
            overflow-y: auto;
        }

        .chart-container {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            height: 200px;
            position: relative;
        }

        .chart-title {
            color: #00ccaa;
            font-size: 0.9em;
            margin-bottom: 10px;
            text-transform: uppercase;
        }

        .chart-canvas {
            width: 100%;
            height: 150px;
        }

        .activity-panel {
            height: 600px;
        }

        .activity-feed {
            background: rgba(0, 0, 0, 0.5);
            border-radius: 10px;
            padding: 15px;
            height: 400px;
            overflow-y: auto;
            margin-bottom: 20px;
        }

        .activity-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px;
            border-bottom: 1px solid rgba(0, 255, 136, 0.2);
            font-size: 0.9em;
        }

        .activity-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7em;
            font-weight: bold;
        }

        .activity-icon.mouse { background: #ff6b6b; }
        .activity-icon.scroll { background: #4ecdc4; }
        .activity-icon.key { background: #45b7d1; }

        .activity-time {
            color: #666;
            font-size: 0.8em;
            min-width: 60px;
        }

        .activity-details {
            flex: 1;
            color: #00ccaa;
        }

        .performance-metrics {
            background: rgba(0, 0, 0, 0.5);
            border-radius: 10px;
            padding: 15px;
        }

        .metric-row {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px solid rgba(0, 255, 136, 0.1);
        }

        .metric-name {
            color: #00ccaa;
            font-size: 0.9em;
        }

        .metric-value {
            color: #00ff88;
            font-weight: bold;
        }

        .bottom-panel {
            background: linear-gradient(135deg, rgba(0, 255, 136, 0.1) 0%, rgba(0, 255, 136, 0.05) 100%);
            border: 2px solid #00ff88;
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .action-btn {
            padding: 15px 30px;
            background: linear-gradient(45deg, #00ff88, #00ccaa);
            border: none;
            border-radius: 25px;
            color: #000;
            font-family: 'Orbitron', monospace;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(0, 255, 136, 0.4);
        }

        .action-btn.secondary {
            background: linear-gradient(45deg, #333, #555);
            color: #00ff88;
        }

        .heatmap-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1000;
        }

        .heatmap-canvas {
            width: 100%;
            height: 100%;
            opacity: 0.6;
        }

        .mouse-cursor {
            position: fixed;
            width: 8px;
            height: 8px;
            background: #00ff88;
            border-radius: 50%;
            pointer-events: none;
            z-index: 1001;
            box-shadow: 0 0 10px #00ff88;
            animation: cursorPulse 1s infinite;
        }

        @keyframes cursorPulse {
            0%, 100% { opacity: 0.8; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
        }

        .velocity-indicator {
            position: fixed;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            padding: 10px 20px;
            border-radius: 20px;
            border: 2px solid #00ff88;
            color: #00ff88;
            font-family: 'Orbitron', monospace;
            z-index: 1002;
            display: none;
        }

        .velocity-indicator.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="heatmap-overlay">
        <canvas class="heatmap-canvas" id="heatmapCanvas"></canvas>
    </div>
    
    <div class="mouse-cursor" id="mouseCursor"></div>
    
    <div class="velocity-indicator" id="velocityIndicator">
        Velocity: <span id="velocityValue">0</span> px/s
    </div>

    <div class="container">
        <div class="header">
            <h1>NEURAL INTERACTION ANALYZER</h1>
            <p class="tagline">Advanced Human Behavior Capture System</p>
        </div>

        <div class="main-grid">
            <div class="panel control-panel">
                <div class="panel-title">Mission Control</div>
                
                <div class="record-control">
                    <button class="record-btn" id="recordBtn" onclick="toggleRecording()">
                        START<br>CAPTURE
                    </button>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <span class="stat-value" id="mouseEvents">0</span>
                        <span class="stat-label">Mouse</span>
                    </div>
                    <div class="stat-card">
                        <span class="stat-value" id="scrollEvents">0</span>
                        <span class="stat-label">Scroll</span>
                    </div>
                    <div class="stat-card">
                        <span class="stat-value" id="keyEvents">0</span>
                        <span class="stat-label">Keys</span>
                    </div>
                    <div class="stat-card">
                        <span class="stat-value" id="duration">00:00</span>
                        <span class="stat-label">Time</span>
                    </div>
                </div>

                <div class="settings-section">
                    <div class="setting-group">
                        <label class="setting-label">Mission ID</label>
                        <input type="text" class="setting-input" id="sessionName" value="NEURAL_001">
                    </div>
                    
                    <div class="setting-group">
                        <label class="setting-label">Capture Rate</label>
                        <input type="number" class="setting-input" id="captureRate" value="100" min="10" max="1000">
                    </div>

                    <div class="setting-group">
                        <label class="setting-label">Data Streams</label>
                        <div class="checkbox-wrapper">
                            <input type="checkbox" class="cyber-checkbox" id="mouseMoves" checked>
                            <label for="mouseMoves">Mouse Movement</label>
                        </div>
                        <div class="checkbox-wrapper">
                            <input type="checkbox" class="cyber-checkbox" id="mouseClicks" checked>
                            <label for="mouseClicks">Click Events</label>
                        </div>
                        <div class="checkbox-wrapper">
                            <input type="checkbox" class="cyber-checkbox" id="scrollEvents" checked>
                            <label for="scrollEvents">Scroll Data</label>
                        </div>
                        <div class="checkbox-wrapper">
                            <input type="checkbox" class="cyber-checkbox" id="keyEvents" checked>
                            <label for="keyEvents">Keyboard Input</label>
                        </div>
                        <div class="checkbox-wrapper">
                            <input type="checkbox" class="cyber-checkbox" id="velocityData" checked>
                            <label for="velocityData">Velocity Analysis</label>
                        </div>
                        <div class="checkbox-wrapper">
                            <input type="checkbox" class="cyber-checkbox" id="accelerationData" checked>
                            <label for="accelerationData">Acceleration Data</label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="panel analytics-panel">
                <div class="panel-title">Real-Time Analytics</div>
                
                <div class="chart-container">
                    <div class="chart-title">Mouse Velocity Over Time</div>
                    <canvas class="chart-canvas" id="velocityChart"></canvas>
                </div>

                <div class="chart-container">
                    <div class="chart-title">Event Frequency</div>
                    <canvas class="chart-canvas" id="frequencyChart"></canvas>
                </div>

                <div class="chart-container">
                    <div class="chart-title">Click Pattern Analysis</div>
                    <canvas class="chart-canvas" id="clickChart"></canvas>
                </div>
            </div>

            <div class="panel activity-panel">
                <div class="panel-title">Live Activity</div>
                
                <div class="activity-feed" id="activityFeed">
                    <div class="activity-item">
                        <div class="activity-icon mouse">M</div>
                        <span class="activity-time">00:00</span>
                        <span class="activity-details">System initialized</span>
                    </div>
                </div>

                <div class="performance-metrics">
                    <div class="metric-row">
                        <span class="metric-name">Avg Velocity</span>
                        <span class="metric-value" id="avgVelocity">0 px/s</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-name">Max Velocity</span>
                        <span class="metric-value" id="maxVelocity">0 px/s</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-name">Click Rate</span>
                        <span class="metric-value" id="clickRate">0/min</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-name">Key Rate</span>
                        <span class="metric-value" id="keyRate">0/min</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-name">Data Points</span>
                        <span class="metric-value" id="dataPoints">0</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="bottom-panel">
            <div class="action-buttons">
                <button class="action-btn" onclick="exportData()">Export Neural Data</button>
                <button class="action-btn secondary" onclick="clearData()">Reset System</button>
                <button class="action-btn secondary" onclick="toggleHeatmap()">Toggle Heatmap</button>
            </div>
        </div>
    </div>

    <script>
        let isRecording = false;
        let recordingData = [];
        let startTime = null;
        let counters = { mouse: 0, scroll: 0, key: 0, clicks: 0 };
        let durationInterval = null;
        let velocities = [];
        let lastMousePos = { x: 0, y: 0, time: 0 };
        let maxVelocity = 0;
        let heatmapVisible = true;

        function toggleRecording() {
            const btn = document.getElementById('recordBtn');
            const cursor = document.getElementById('mouseCursor');
            const velocityIndicator = document.getElementById('velocityIndicator');

            if (!isRecording) {
                startRecording();
                btn.textContent = 'STOP\nCAPTURE';
                btn.classList.add('recording');
                cursor.style.display = 'block';
                velocityIndicator.classList.add('active');
            } else {
                stopRecording();
                btn.textContent = 'START\nCAPTURE';
                btn.classList.remove('recording');
                cursor.style.display = 'none';
                velocityIndicator.classList.remove('active');
            }
        }

        function startRecording() {
            isRecording = true;
            startTime = Date.now();
            recordingData = [];
            counters = { mouse: 0, scroll: 0, key: 0, clicks: 0 };
            velocities = [];
            maxVelocity = 0;
            
            durationInterval = setInterval(updateDuration, 1000);
            
            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('click', handleClick);
            document.addEventListener('scroll', handleScroll);
            document.addEventListener('keydown', handleKeyDown);
            
            addActivity('System started recording', 'system');
        }

        function stopRecording() {
            isRecording = false;
            clearInterval(durationInterval);
            
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('click', handleClick);
            document.removeEventListener('scroll', handleScroll);
            document.removeEventListener('keydown', handleKeyDown);
            
            addActivity('Recording stopped', 'system');
        }

        function handleMouseMove(e) {
            if (!isRecording) return;
            
            const now = Date.now();
            const velocity = calculateVelocity(e.clientX, e.clientY, now);
            
            const data = {
                type: 'mousemove',
                timestamp: now - startTime,
                x: e.clientX,
                y: e.clientY,
                pageX: e.pageX,
                pageY: e.pageY,
                movementX: e.movementX,
                movementY: e.movementY,
                velocity: velocity,
                acceleration: calculateAcceleration(velocity),
                target: e.target.tagName,
                buttons: e.buttons
            };
            
            recordingData.push(data);
            counters.mouse++;
            updateCounter('mouseEvents', counters.mouse);
            
            // Update cursor position
            const cursor = document.getElementById('mouseCursor');
            cursor.style.left = e.clientX + 'px';
            cursor.style.top = e.clientY + 'px';
            
            // Update velocity display
            document.getElementById('velocityValue').textContent = Math.round(velocity);
            
            // Update heatmap
            if (heatmapVisible) {
                updateHeatmap(e.clientX, e.clientY, velocity);
            }
            
            // Update charts
            updateVelocityChart(velocity);
            updatePerformanceMetrics();
        }

        function handleClick(e) {
            if (!isRecording) return;
            
            const data = {
                type: 'click',
                timestamp: Date.now() - startTime,
                x: e.clientX,
                y: e.clientY,
                button: e.button,
                detail: e.detail,
                ctrlKey: e.ctrlKey,
                altKey: e.altKey,
                shiftKey: e.shiftKey,
                metaKey: e.metaKey,
                target: e.target.tagName
            };
            
            recordingData.push(data);
            counters.clicks++;
            addActivity(`Click at (${e.clientX}, ${e.clientY})`, 'mouse');
        }

        function handleScroll(e) {
            if (!isRecording) return;
            
            const data = {
                type: 'scroll',
                timestamp: Date.now() - startTime,
                scrollX: window.scrollX,
                scrollY: window.scrollY,
                deltaX: e.deltaX,
                deltaY: e.deltaY,
                deltaZ: e.deltaZ,
                deltaMode: e.deltaMode
            };
            
            recordingData.push(data);
            counters.scroll++;
            updateCounter('scrollEvents', counters.scroll);
            addActivity(`Scroll Δ(${Math.round(e.deltaX)}, ${Math.round(e.deltaY)})`, 'scroll');
        }

        function handleKeyDown(e) {
            if (!isRecording) return;
            
            const data = {
                type: 'keydown',
                timestamp: Date.now() - startTime,
                key: e.key,
                code: e.code,
                keyCode: e.keyCode,
                location: e.location,
                repeat: e.repeat,
                ctrlKey: e.ctrlKey,
                altKey: e.altKey,
                shiftKey: e.shiftKey,
                metaKey: e.metaKey,
                target: e.target.tagName
            };
            
            recordingData.push(data);
            counters.key++;
            updateCounter('keyEvents', counters.key);
            addActivity(`Key: ${e.key}`, 'key');
        }

        function calculateVelocity(x, y, time) {
            if (lastMousePos.time === 0) {
                lastMousePos = { x, y, time };
                return 0;
            }
            
            const deltaX = x - lastMousePos.x;
            const deltaY = y - lastMousePos.y;
            const deltaTime = time - lastMousePos.time;
            
            const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
            const velocity = deltaTime > 0 ? (distance / deltaTime) * 1000 : 0;
            
            lastMousePos = { x, y, time };
            velocities.push(velocity);
            
            if (velocity > maxVelocity) {
                maxVelocity = velocity;
            }
            
            return velocity;
        }

        function calculateAcceleration(currentVelocity) {
            if (velocities.length < 2) return 0;
            const previousVelocity = velocities[velocities.length - 2];
            return currentVelocity - previousVelocity;
        }

        function updateCounter(id, value) {
            document.getElementById(id).textContent = value;
        }

        function updateDuration() {
            if (!startTime) return;
            const elapsed = Math.floor((Date.now() - startTime) / 1000);
            const minutes = Math.floor(elapsed / 60);
            const seconds = elapsed % 60;
            document.getElementById('duration').textContent = 
                `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }

        function addActivity(message, type) {
            const feed = document.getElementById('activityFeed');
            const item = document.createElement('div');
            item.className = 'activity-item';
            
            const time = new Date().toLocaleTimeString().split(':').slice(1).join(':');
            const iconMap = { mouse: 'M', scroll: 'S', key: 'K', system: '●' };
            
            item.innerHTML = `
                <div class="activity-icon ${type}">${iconMap[type] || '?'}</div>
                <span class="activity-time">${time}</span>
                <span class="activity-details">${message}</span>
            `;
            
            feed.appendChild(item);
            feed.scrollTop = feed.scrollHeight;
            
            if (feed.children.length > 50) {
                feed.removeChild(feed.firstChild);
            }
        }

        function updateHeatmap(x, y, velocity) {
            const canvas = document.getElementById('heatmapCanvas');
            const ctx = canvas.getContext('2d');
            
            if (canvas.width !== window.innerWidth || canvas.height !== window.innerHeight) {
                canvas.width = window.innerWidth;
                canvas.height = window.innerHeight;
            }
            
            const intensity = Math.min(velocity / 1000, 1);
            const radius = 15 + (intensity * 10);
            
            const gradient = ctx.createRadialGradient(x, y, 0, x, y, radius);
            gradient.addColorStop(0, `rgba(0, 255, 136, ${intensity * 0.3})`);
            gradient.addColorStop(1, 'rgba(0, 255, 136, 0)');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(x - radius, y - radius, radius * 2, radius * 2);
        }

        function updateVelocityChart(velocity) {
            // Simplified chart update - in a real implementation, use Chart.js or similar
            const canvas = document.getElementById('velocityChart');
            const ctx = canvas.getContext('2d');
            
            if (canvas.width !== canvas.offsetWidth) {
                canvas.width = canvas.offsetWidth;
                canvas.height = canvas.offsetHeight;
            }
            
            // Simple line graph simulation
            ctx.strokeStyle = '#00ff88';
            ctx.lineWidth = 2;
            ctx.beginPath();
            
            const recentVelocities = velocities.slice(-50);
            const step = canvas.width / 50;
            
            recentVelocities.forEach((v, i) => {
                const x = i * step;
                const y = canvas.height - (v / 1000) * canvas.height;
                if (i === 0) ctx.moveTo(x, y);
                else ctx.lineTo(x, y);
            });
            
            ctx.stroke();
        }

        function updatePerformanceMetrics() {
            if (velocities.length === 0) return;
            
            const avgVelocity = velocities.reduce((a, b) => a + b, 0) / velocities.length;
            const elapsedMinutes = ((Date.now() - startTime) / 1000) / 60;
            
            document.getElementById('avgVelocity').textContent = Math.round(avgVelocity) + ' px/s';
            document.getElementById('maxVelocity').textContent = Math.round(maxVelocity) + ' px/s';
            document.getElementById('clickRate').textContent = Math.round(counters.clicks / elapsedMinutes) + '/min';
            document.getElementById('keyRate').textContent = Math.round(counters.key / elapsedMinutes) + '/min';
            document.getElementById('dataPoints').textContent = recordingData.length;
        }

        function exportData() {
            if (recordingData.length === 0) {
                alert('No neural data to export. Start capture first.');
                return;
            }
            
            const sessionName = document.getElementById('sessionName').value;
            const analysisData = {
                sessionId: sessionName,
                timestamp: new Date().toISOString(),
                captureRate: document.getElementById('captureRate').value,
                totalEvents: recordingData.length,
                counters: counters,
                performance: {
                    avgVelocity: velocities.reduce((a, b) => a + b, 0) / velocities.length,
                    maxVelocity: maxVelocity,
                    totalVelocityPoints: velocities.length
                },
                neuralData: recordingData
            };
            
            const blob = new Blob([JSON.stringify(analysisData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `neural_${sessionName}_${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
            
            addActivity('Neural data exported', 'system');
        }

        function clearData() {
            if (confirm('Reset all neural data? This action cannot be undone.')) {
                recordingData = [];
                counters = { mouse: 0, scroll: 0, key: 0, clicks: 0 };
                velocities = [];
                maxVelocity = 0;
                
                updateCounter('mouseEvents', 0);
                updateCounter('scrollEvents', 0);
                updateCounter('keyEvents', 0);
                document.getElementById('duration').textContent = '00:00';
                
                // Clear charts and heatmap
                const canvases = ['heatmapCanvas', 'velocityChart', 'frequencyChart', 'clickChart'];
                canvases.forEach(id => {
                    const canvas = document.getElementById(id);
                    const ctx = canvas.getContext('2d');
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                });
                
                // Clear activity feed
                document.getElementById('activityFeed').innerHTML = `
                    <div class="activity-item">
                        <div class="activity-icon system">●</div>
                        <span class="activity-time">00:00</span>
                        <span class="activity-details">System reset</span>
                    </div>
                `;
                
                updatePerformanceMetrics();
            }
        }

        function toggleHeatmap() {
            heatmapVisible = !heatmapVisible;
            const canvas = document.getElementById('heatmapCanvas');
            canvas.style.display = heatmapVisible ? 'block' : 'none';
        }

        // Initialize
        window.addEventListener('resize', () => {
            const canvas = document.getElementById('heatmapCanvas');
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        });
    </script>
</body>
</html>
